"use client";

import { getClientSecretSignUp } from "@/api/stripe";
import Loading from "@/components/Loading";
import SignUpContent from "@/components/SignUpContent";
import { useStripe } from "@/context/StripeContext";
import withAuth from "@/hoc/withAuth";
import { useClient } from "@/hooks/useClient";
import useFlowRedirect from "@/hooks/useFlowRedirect";
import { CheckoutProvider } from "@stripe/react-stripe-js";
import {
  StripeCheckoutElementsOptions,
  StripeElementsOptions,
} from "@stripe/stripe-js";
import { useEffect, useState } from "react";
import StripeForm from "./_components/StripeForm";
import { currencyRounded } from "@/helpers/numbers";
import { epochToDate, getHumanReadableDateFormat } from "@/helpers/dates";

const elementsOptions: StripeCheckoutElementsOptions = {
  appearance: {
    theme: "flat",
    rules: {
      ".AccordionItem": {
        backgroundColor: "transparent",
        borderColor: "transparent",
        paddingLeft: "1px",
        paddingRight: "1px",
      },
      ".Label": {
        color: "white",
        marginBottom: "8px",
      },
      ".Input": {
        marginBottom: "8px",
        borderRadius: "8px",
        color: "white",
      },
      ".Input:focus": {
        boxShadow: "none",
        outline: "1px solid  #FDFE00",
      },
      ".Input--invalid": {
        boxShadow: "none",
        color: "white",
      },
      ".Error": {
        color: "#FF5A1E",
        fontSize: "12px",
      },
    },
    variables: {
      colorPrimary: "white",
      colorBackground: "#ffffff1a",
      iconCardErrorColor: "#FF5A1E",
      iconCardCvcErrorColor: "#FF5A1E",
    },
  },
};

function Payment() {
  const { stripePromise } = useStripe();
  const { planId, client } = useClient();
  const selectedPlan = client?.plans.find((plan) => plan.id === planId);

  const [options, setOptions] = useState<
    | (StripeElementsOptions & { billingCycle: number; amount_total: number })
    | null
  >(null);

  const finalPrice = (options?.amount_total || 0) / 100;

  useFlowRedirect({ fetchSubscriberOnRender: true });

  useEffect(() => {
    getClientSecretSignUp(planId, "/payment/return/").then((res) => {
      setOptions({
        clientSecret: res.client_secret,
        billingCycle: res.billing_cycle || 0,
        amount_total: res.amount_total || 0,
      });
    });
  }, [planId]);

  return (
    <SignUpContent stepIndex={5} biggerRight alignTop>
      <SignUpContent.Left
        title="Payment"
        description="Fill in your details to start simping"
      />
      <SignUpContent.Right>
        <div className="overflow-auto rounded-2xl md:mt-20">
          <div className="md:p-8 md:bg-[#d9d9d91a] rounded-2xl md:px-10">
            <div className="mx-auto md:min-h-[376px] flex flex-col">
              {!options ? (
                <div className="grow flex justify-center items-center">
                  <Loading />
                </div>
              ) : (
                <>
                  <div className="flex justify-between items-end mb-4">
                    <h3 className="text-2xl font-semibold">
                      {selectedPlan?.title}
                    </h3>
                    <span className="text-3xl font-semibold">
                      {currencyRounded(finalPrice)}
                    </span>
                  </div>
                  <div>
                    Then {currencyRounded(Number(selectedPlan?.current_price))}{" "}
                    per month starting on{" "}
                    {getHumanReadableDateFormat(
                      epochToDate(options?.billingCycle || 0),
                    )}
                  </div>

                  <div className="h-[1px] bg-[#868686] mt-4" />
                  <CheckoutProvider
                    stripe={stripePromise}
                    options={{
                      clientSecret: options.clientSecret || "",
                      elementsOptions,
                    }}
                  >
                    <StripeForm />
                  </CheckoutProvider>
                </>
              )}
            </div>
          </div>
        </div>
      </SignUpContent.Right>
    </SignUpContent>
  );
}

export default withAuth(Payment);
