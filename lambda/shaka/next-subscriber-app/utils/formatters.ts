export const capitalize = (str: string): string => {
  if (str.includes('_') || str.includes('-')) {
    return str
      .split('_')
      .map((word) => capitalize(word))
      .join(' ');
  }
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export function penniesToPoundsString(pennies: number | undefined): string {
  if (!pennies) return '';

  const pounds = pennies / 100;
  return `£${pounds.toFixed(2)}`;
}

// extract unit tests to global as well
export function getDayMonthYearFromDate(date: string) {
  if (!date) return { year: '', month: '', day: '' };

  const [year, month, day] = date.split('-');
  return { year, month, day };
}

export const trimWhiteSpace = (str: string): string => {
  return str.trim();
};

export function getQRCodeDataUrl(base64Data: string | undefined) {
  if (!base64Data) return '';
  if (base64Data.startsWith('data:')) return base64Data;
  return `data:image/png;base64,${base64Data}`;
}
