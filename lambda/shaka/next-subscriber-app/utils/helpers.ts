import type {
  MainPlan,
  SimsApiResponse,
  SubscriptionsApiResponse
} from '@/schemas/schemas';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { LocalKey } from '@/hooks/useLocalStorage';
import { PurchaseIntentDetails } from '@/services/paymentService';

export function getStepFromPath<TRoutes extends Record<string, string>>(
  stepRoutes: TRoutes,
  pathname: string | null
): keyof TRoutes | undefined {
  if (pathname === null || pathname === undefined) return;

  if (pathname === '' || pathname === '/') {
    for (const [step, route] of Object.entries(stepRoutes)) {
      if (route === '') {
        return step as keyof TRoutes;
      }
    }
    return undefined;
  }

  const pathSegments = pathname.split('/').filter(Boolean);
  const lastSegment = pathSegments[pathSegments.length - 1] || '';

  for (const [step, route] of Object.entries(stepRoutes)) {
    if (route === lastSegment) {
      return step as keyof TRoutes;
    }
  }

  return undefined;
}

export const createStepRoutes = (steps: readonly string[]) => {
  return steps.reduce<Record<string, string>>((acc, step) => {
    acc[step] = step;
    return acc;
  }, {});
};

export type ValidationErrorObject = {
  _errors?: string[];
  [key: string]: any;
};

export const flattenValidationErrors = (
  errors: ValidationErrorObject,
  parentKey?: string
): string[] => {
  const result: string[] = [];

  if (errors._errors) {
    const hasErrors =
      Array.isArray(errors._errors) && errors._errors.length > 0;
    if (hasErrors) {
      const prefix = parentKey ? `${parentKey}: ` : '';
      result.push(...errors._errors.map((msg) => `${prefix}${msg}`));
    }
  }

  for (const key of Object.keys(errors)) {
    if (key === '_errors') continue;
    const value = errors[key];
    if (typeof value === 'object' && value !== null) {
      result.push(...flattenValidationErrors(value, key));
    }
  }

  return result;
};

// export const isUKPublicHoliday = (date: Date): boolean => {
//   const dateString = date.toISOString().split('T')[0];
//   return UK_PUBLIC_HOLIDAYS.includes(dateString);
// };

// export const isLeapYear = (year) => {
//   const fullYear = year < 100 ? 2000 + parseInt(year) : parseInt(year);
//   return (fullYear % 4 === 0 && fullYear % 100 !== 0) || fullYear % 400 === 0;
// };

// account for stac code as well !!
export function formatPortingCode(code: string): string {
  if (!code) return '';
  return code.replace(/\s+/g, '').toUpperCase();
}

export const checkIsSafari = () => {
  if (typeof window === 'undefined') return false;
  const userAgent = window.navigator.userAgent;
  return (
    /Safari/.test(userAgent) && !/Chrome|CriOS|FxiOS|EdgiOS/.test(userAgent)
  );
};

export const checkIsAndroid = () => {
  if (typeof window === 'undefined') return false;
  const userAgent = window.navigator.userAgent;
  return /Android/.test(userAgent);
};

export const checkIsIos = () => {
  if (typeof window === 'undefined') return false;
  const userAgent = window.navigator.userAgent;
  return /iPad|iPhone|iPod/.test(userAgent);
};

export const checkIsDesktop = () => {
  if (typeof window === 'undefined') return false;
  const userAgent = window.navigator.userAgent;
  return !/Mobile/.test(userAgent);
};

export const standariseNetworkError = (error: any) => {
  if (error.code === 'paymentFailed' && error.message) {
    return [
      `There was an issue with your payment and it did not complete. ${error.message}`
    ];
  }
  if (
    error.message?.includes('Network Error') ||
    error.message?.includes('timeout') ||
    error.message?.includes('fetch') ||
    error.message?.includes('Failed to fetch') ||
    error.name === 'NetworkError' ||
    error.name === 'TypeError' ||
    error.code === 'NETWORK_ERROR' ||
    error.code === 'ERR_NETWORK' ||
    !error.response
  ) {
    return ['Network error occurred'];
  }

  if (error.response?.data?.error) {
    return [error.response.data.error];
  }

  if (error.response?.data?.message) {
    return [error.response.data.message];
  }

  return ['Something went wrong, please try again later.'];
};

export const createTestId = (name: string) => {
  if (!name) return '';
  return name.toLowerCase().replace(/\s+/g, '-');
};

export function getNextWorkingDay(
  startDate: Date,
  holidayList: string[]
): Date {
  const candidateDate = new Date(startDate);

  while (true) {
    candidateDate.setDate(candidateDate.getDate() + 1);
    const isoDate = candidateDate.toISOString().slice(0, 10);
    const isWeekend =
      candidateDate.getDay() === 0 || candidateDate.getDay() === 6;
    const isHoliday = holidayList.includes(isoDate);

    if (!isWeekend && !isHoliday) {
      break;
    }
  }
  return candidateDate;
}

export function getAvailableDatesRange(
  startDate: Date,
  numberOfDays: number,
  holidayList: string[] = []
): Date[] {
  const availableDates: Date[] = [];
  const candidateDate = new Date(startDate);
  const maxDate = new Date(startDate);
  maxDate.setDate(startDate.getDate() + 30);

  while (true) {
    const hasReachedDateLimit = candidateDate >= maxDate;
    const hasReachedWorkingDayLimit = availableDates.length >= numberOfDays;

    if (hasReachedDateLimit || hasReachedWorkingDayLimit) {
      break;
    }

    candidateDate.setDate(candidateDate.getDate() + 1);
    const isoDate = candidateDate.toISOString().slice(0, 10);
    const isWeekend =
      candidateDate.getDay() === 0 || candidateDate.getDay() === 6;
    const isHoliday = holidayList.includes(isoDate);

    if (!isWeekend && !isHoliday) {
      availableDates.push(new Date(candidateDate));
    }
  }
  return availableDates;
}

// add tests
export function getRemainingDays(
  periodEnd: Date | string,
  currentDate: Date | string = new Date()
): number {
  const endDate = periodEnd instanceof Date ? periodEnd : new Date(periodEnd);

  const startDate =
    currentDate instanceof Date ? currentDate : new Date(currentDate);

  const MILLISECONDS_PER_DAY = 1000 * 60 * 60 * 24;

  const normalizedEndDate = new Date(
    endDate.getFullYear(),
    endDate.getMonth(),
    endDate.getDate()
  );
  const normalizedStartDate = new Date(
    startDate.getFullYear(),
    startDate.getMonth(),
    startDate.getDate()
  );

  const timeDiff = normalizedEndDate.getTime() - normalizedStartDate.getTime();
  return Math.ceil(timeDiff / MILLISECONDS_PER_DAY);
}

export function constructRemainingDaysMessage(
  remainingDays: number
): string | null {
  if (remainingDays === 0) return null;
  if (remainingDays === 1) return '1 day left';
  return `${remainingDays} days left`;
}

export interface OrderItem {
  id: number;
  name: string;
  price: number;
  description?: Record<string, string>;
}

export interface OrderItemWithQuantity extends OrderItem {
  quantity: number;
  displayName: string;
}

export function calculateTotalCost(
  orderItems: OrderItemWithQuantity[]
): string {
  return orderItems
    .reduce((total, item) => total + item.price * item.quantity, 0)
    .toString();
}

export function getFieldError(fieldName: string, errors: string[]) {
  const error = errors.find((error) => error?.startsWith(`${fieldName}:`));
  return error ? error.replace(`${fieldName}: `, '') : null;
}

export function selectAvailableId(
  storedId: number | undefined,
  availableIds: number[],
  fallbackId?: number
): number | undefined {
  if (!availableIds || availableIds.length === 0) {
    return storedId ?? fallbackId;
  }

  const availableSet = new Set(availableIds);

  if (storedId !== undefined && availableSet.has(storedId)) return storedId;
  if (fallbackId !== undefined && availableSet.has(fallbackId))
    return fallbackId;

  return availableIds[0];
}

export function availableMainPlanIds(
  planData: MainPlan[] | undefined
): number[] {
  return planData?.map((p) => p.id) ?? [];
}

export function availableFamilyPlanIds(
  planData: MainPlan[] | undefined
): number[] {
  return (
    planData?.flatMap((p) => p.family_plans?.map((fp) => fp.id) ?? []) ?? []
  );
}

// tests !!
export function hasAlreadyRestoredSignupStep() {
  return localStorage.getItem(LocalKey.SIGNUP_STEP_RESTORED) === '1';
}

export function isValidSignupPath(path) {
  const validSignupPaths = new Set(
    Object.values(ROUTES_CONFIG).map((route) => route.path)
  );
  return validSignupPaths.has(path);
}

export function shouldRestoreToSavedPath(
  savedPath: string,
  currentPath: string
) {
  return savedPath && savedPath !== currentPath;
}

export function markSignupStepAsRestored() {
  sessionStorage.setItem(LocalKey.SIGNUP_STEP_RESTORED, '1');
}

type Brand = 'deliveroo' | 'uswitch';

type CurrentSignupStep<TSteps, B extends Brand = Brand> = {
  brand: B;
  stepId: keyof TSteps;
  at: number;
};

export function getCurrentSignupStep<
  TSteps,
  B extends Brand = Brand
>(): CurrentSignupStep<TSteps, B> {
  try {
    return JSON.parse(localStorage.getItem(LocalKey.SIGNUP_STEP) || '{}');
  } catch {
    console.error('Failed to parse signup step from localStorage');
    return {} as CurrentSignupStep<TSteps, B>;
  }
}

export function extractCorrelationIdsFromBasket(
  details: PurchaseIntentDetails
): string[] {
  return details.purchase_details.basket.map((item) => item.correlation_id);
}

export function findSimsByCorrelationIds(
  correlationIds: string[],
  sims: SimsApiResponse
): SimsApiResponse {
  const simLookupMap = new Map(
    sims.map((simCard) => [simCard.correlation_id, simCard])
  );

  return correlationIds
    .map((correlationId) => simLookupMap.get(correlationId))
    .filter((sim): sim is NonNullable<typeof sim> => sim !== undefined);
}

export function filterSubscriptionsByCorrelationIds(
  correlationIds: string[],
  subscriptions: SubscriptionsApiResponse
): SubscriptionsApiResponse {
  const correlationIdSet = new Set(correlationIds);
  return subscriptions.filter(
    (subscription) =>
      subscription.correlation_id &&
      correlationIdSet.has(subscription.correlation_id)
  );
}
