import { AxiosClient } from '@/lib/axios-client';
import { API_ENDPOINTS, API_HOST } from '@/auth/api/endpoints';
import { SessionResponse } from '@/services/paymentService';
import { NumberPortingFormData } from '@/schema/schema';
import { getClientConfig } from '@/client-config/client-config';

export const wizardService = {
  otp: async (apiClient: AxiosClient, otp: string): Promise<any> => {
    return await apiClient.post(`${API_HOST}/s/api/v1/1/${API_ENDPOINTS.otp}`, {
      code: otp
    });
  },
  otpResend: async (apiClient: AxiosClient): Promise<any> => {
    return await apiClient.post(
      `${API_HOST}/s/api/v1/1/${API_ENDPOINTS.auth.otpResend}`
    );
    // response.data ? or just response ?
    // const response = await apiClient.post(API_ENDPOINTS.auth.otp, data);
  },

  submitPACForm: async (
    apiClient: AxiosClient,
    data: NumberPortingFormData & { subscription_id: number }
  ): Promise<SessionResponse> => {
    const response = await apiClient.post(
      API_ENDPOINTS.wizard.pacCode(data.subscription_id.toString()),
      data
    );
    return response.data;
  },
  submitPurchaseDetails: async (apiClient: AxiosClient, data: any) => {
    const response = await apiClient.post(API_ENDPOINTS.plans.base, data);
    return response.data;
  },
  getIphoneInstallationCode: async (apiClient: AxiosClient) => {
    return await apiClient.get(API_ENDPOINTS.wizard.iphoneInstallationCode());
  },
  // todo: add type
  getUniversalLink: async (
    apiClient: AxiosClient,
    token: string
  ): Promise<any> => {
    const clientId = getClientConfig().clientId;
    return await apiClient.post(
      `http://localhost:8000/s/api/v1/${clientId}/${API_ENDPOINTS.wizard.universalLink(token)}`
    );
    // return await apiClient.get(API_ENDPOINTS.wizard.universalLink());
  }
};
