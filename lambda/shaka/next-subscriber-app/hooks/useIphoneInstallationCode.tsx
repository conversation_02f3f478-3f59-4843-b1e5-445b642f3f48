import { useQuery } from '@tanstack/react-query';
import { signupKeys } from '@/query-keys/query-keys';
import { useAuth } from '@/auth/hooks/use-auth';
import { wizardService } from '@/services/wizardService';

export function useIphoneInstallationCode() {
  const { apiClient } = useAuth();
  const { data, isPending, error } = useQuery({
    queryKey: signupKeys.iphoneInstallationCode,
    queryFn: () => wizardService.getIphoneInstallationCode(apiClient)
  });

  return {
    installationCode: data?.code,
    isPendingInstallationCode: isPending,
    iphoneInstallationCodeError: error
  };
}
