import { useMutation } from '@tanstack/react-query';
import { useCheckout } from '@stripe/react-stripe-js';
import { AxiosError } from 'axios';
import { ConfirmError } from '@stripe/stripe-js';
import { standariseNetworkError } from '@/utils/helpers';
import { useState, useCallback } from 'react';

/**
 * Custom payment form submission hook for modals that doesn't redirect on success
 * Unlike usePaymentFormSubmission, this hook allows custom success handling
 */
export function useModalPaymentFormSubmission() {
  const { confirm } = useCheckout();
  const [errors, setErrors] = useState<string[]>([]);

  const { mutateAsync: submitPayment, isPending } = useMutation({
    mutationFn: async () => {
      const result = await confirm();
      if (result.type === 'error') {
        throw result.error;
      }
      return result;
    },
    // No onSuccess redirect - let the caller handle success
    onError: (error: ConfirmError | AxiosError) =>
      setErrors(standariseNetworkError(error))
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors([]); // Clear previous errors
    return await submitPayment();
  };

  const handleExpressCheckout = useCallback(async () => {
    try {
      setErrors([]);
      return await submitPayment();
    } catch (error) {
      setErrors(standariseNetworkError(error));
      throw error; // Re-throw so caller can handle
    }
  }, [submitPayment]);

  const handleExpressCancel = useCallback(() => {
    setErrors([]);
  }, []);

  const handleExpressLoadError = useCallback(
    (event: { error: { message?: string } }) => {
      const errorMessage =
        event.error.message || 'Failed to load express payment method';
      setErrors([errorMessage]);
    },
    []
  );

  return {
    handleSubmit,
    handleExpressCheckout,
    handleExpressCancel,
    handleExpressLoadError,
    isPending,
    errors,
    setErrors
  };
}
