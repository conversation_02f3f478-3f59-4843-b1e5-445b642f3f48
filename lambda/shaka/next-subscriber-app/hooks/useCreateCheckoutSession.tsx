import { useMutation } from '@tanstack/react-query';
import { paymentService } from '@/services/paymentService';
import { useAuth } from '@/auth/hooks/use-auth';
import { StripeMode } from '@/lib/stripe/types';

interface CheckoutSessionParams {
  clientId: string;
  paymentMode: StripeMode;
  returnUrl: string;
  basket: any;
}

export function useCreateCheckoutSession() {
  const { apiClient } = useAuth();

  const {
    mutate: createCheckoutSession,
    data: clientSecret,
    isPending,
    error,
    isError
  } = useMutation({
    mutationFn: ({ clientId, paymentMode, returnUrl, basket }: CheckoutSessionParams) =>
      paymentService.createCheckoutSession(
        apiClient,
        clientId,
        paymentMode,
        returnUrl,
        { basket }
      )
  });

  return {
    createCheckoutSession,
    clientSecret,
    isPending,
    error,
    isError
  };
}
