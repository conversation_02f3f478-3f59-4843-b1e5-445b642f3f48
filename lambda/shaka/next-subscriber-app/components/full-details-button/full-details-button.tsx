import React, { useState } from 'react';
import { sendGTMEvent } from '@next/third-parties/google';

interface FullDetailsButtonProps {
  text: string;
  className?: string;
  children: (props: {
    isOpen: boolean;
    setIsOpen: (open: boolean) => void;
  }) => React.ReactNode;
}

export function FullDetailsButton({
  text,
  children,
  className = 'uswitchLink'
}: FullDetailsButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleClick = () => {
    sendGTMEvent({
      event: 'FullDetailsButtonClickTest',
      label: 'FullDetailsButton',
      category: 'Open modal',
      action: 'Click',
      value: 'Pawel'
    });

    setIsOpen(true);
  };

  return (
    <>
      <button
        onClick={handleClick}
        className={`cursor-pointer ${className || ''}`}
      >
        {text}
      </button>
      {children({ isOpen, setIsOpen })}
    </>
  );
}
