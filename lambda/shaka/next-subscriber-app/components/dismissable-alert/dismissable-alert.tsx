import React, { useState, useEffect } from 'react';
import { CloseIcon, ErrorIcon, InfoIcon, UserIcon } from '@/icons/icons';

export type ErrorMessage = string | `${string}: ${string}` | React.ReactNode;
export type ErrorMessages = ErrorMessage | ErrorMessage[];

const ALERT_VARIANT_CONFIGURATIONS = {
  error: {
    icon: <ErrorIcon stroke="black" />,
    bgColor: 'bg-error-subtle',
    borderColor: 'border-error',
    textColor: 'text-primary',
    iconColor: 'text-primary',
    errorMsgColor: 'text-primary',
    role: 'alert',
    ariaLabel: 'Error messages'
  },
  warning: {
    icon: <ErrorIcon stroke="black" />,
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    textColor: 'text-yellow-800',
    iconColor: 'text-yellow-500',
    errorMsgColor: 'text-yellow-800',
    role: 'alert',
    ariaLabel: 'Warning messages'
  },
  success: {
    icon: <InfoIcon />,
    bgColor: 'bg-mint-subtle',
    borderColor: 'border-success-border',
    textColor: 'text-primary',
    iconColor: 'text-primary',
    errorMsgColor: 'text-primary',
    role: 'status',
    ariaLabel: 'Success messages'
  },
  info: {
    icon: <ErrorIcon stroke="black" />,
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    textColor: 'text-blue-800',
    iconColor: 'text-blue-500',
    errorMsgColor: 'text-blue-800',
    role: 'status',
    ariaLabel: 'Information messages'
  },
  deliveroo: {
    icon: <ErrorIcon stroke="black" />,
    bgColor: 'bg-teal-light',
    borderColor: 'border-[#43CCBC]',
    textColor: 'text-text',
    iconColor: 'text-red-500',
    errorMsgColor: 'text-text',
    role: 'status',
    ariaLabel: 'Success message'
  },
  deliverooInfo: {
    icon: <UserIcon />,
    bgColor: 'bg-[#FEF2E0]',
    borderColor: 'border-[#C70]',
    textColor: 'text-text',
    iconColor: 'text-red-500',
    errorMsgColor: 'text-text',
    role: 'status',
    ariaLabel: 'Success message'
  }
} as const;

type AlertVariant = keyof typeof ALERT_VARIANT_CONFIGURATIONS;

const AlertContext = React.createContext<{
  config: (typeof ALERT_VARIANT_CONFIGURATIONS)[AlertVariant];
  onDismiss?: () => void;
}>({
  config: ALERT_VARIANT_CONFIGURATIONS.error
});

function useAutoHide(
  enabled: boolean,
  delay: number,
  onHide: () => void,
  hasContent: boolean
) {
  useEffect(() => {
    if (enabled && hasContent) {
      const timer = setTimeout(onHide, delay);
      return () => clearTimeout(timer);
    }
  }, [enabled, delay, hasContent, onHide]);
}

function normalizeMessages(messages: ErrorMessages): ErrorMessage[] {
  return Array.isArray(messages) ? messages : [messages];
}

function hasMessageContent(messages: ErrorMessages): boolean {
  const normalized = normalizeMessages(messages);
  return (
    normalized.length > 0 &&
    normalized.some((msg) => {
      if (React.isValidElement(msg)) return true;
      if (typeof msg === 'string') return msg.trim().length > 0;
      return false;
    })
  );
}

function processMessageContent(message: ErrorMessage): React.ReactNode {
  if (React.isValidElement(message)) {
    return message;
  }

  if (typeof message === 'string') {
    const colonIndex = message.indexOf(':');
    return colonIndex !== -1
      ? message.substring(colonIndex + 1).trim()
      : message;
  }

  return message;
}

interface AlertContainerProps {
  variant: AlertVariant;
  className?: string;
  children: React.ReactNode;
  onDismiss?: () => void;
}

function AlertContainer({
  variant,
  className = '',
  children,
  onDismiss
}: AlertContainerProps) {
  const config = ALERT_VARIANT_CONFIGURATIONS[variant];
  const { bgColor, borderColor, textColor, role, ariaLabel } = config;
  const ariaLive = role === 'alert' ? 'assertive' : 'polite';

  return (
    <AlertContext.Provider value={{ config, onDismiss }}>
      <div
        role={role}
        aria-live={ariaLive}
        aria-label={ariaLabel}
        className={`${bgColor} ${borderColor} ${textColor} rounded-lg border p-4 ${className}`}
      >
        <div className="flex items-start gap-3">{children}</div>
      </div>
    </AlertContext.Provider>
  );
}

function AlertIcon() {
  const { config } = React.useContext(AlertContext);
  return <>{config.icon}</>;
}

interface MessageContentProps {
  messages: ErrorMessages;
  messagesStyles?: string;
  singleMessageStyles?: string;
}

function MessageContent({
  messages,
  messagesStyles = '',
  singleMessageStyles = ''
}: MessageContentProps) {
  const { config } = React.useContext(AlertContext);
  const normalizedMessages = normalizeMessages(messages);
  const isSingleMessage = normalizedMessages.length === 1;

  if (isSingleMessage && React.isValidElement(normalizedMessages[0])) {
    return (
      <div
        className={`${config.errorMsgColor} ${singleMessageStyles}`}
        role="alert"
      >
        {normalizedMessages[0]}
      </div>
    );
  }

  if (isSingleMessage) {
    const content = processMessageContent(normalizedMessages[0]);
    return (
      <ul className="text-default">
        <li
          className={`leading-5 ${config.errorMsgColor} ${singleMessageStyles}`}
          role="alert"
        >
          {content}
        </li>
      </ul>
    );
  }

  return (
    <ul className={`text-default space-y-1 text-pretty ${messagesStyles}`}>
      {normalizedMessages.map((message, index) => {
        const content = processMessageContent(message);
        return (
          <li key={index} className="leading-5" role="alert">
            {content}
          </li>
        );
      })}
    </ul>
  );
}

function DismissButton() {
  const { config, onDismiss } = React.useContext(AlertContext);

  if (!onDismiss) return null;

  return (
    <button
      onClick={onDismiss}
      className={`${config.iconColor} -m-1 cursor-pointer rounded p-1 transition-opacity hover:opacity-75 focus:ring-2 focus:ring-current focus:ring-offset-1 focus:outline-none`}
      aria-label="Dismiss alert"
    >
      <CloseIcon className="h-4 w-4" />
    </button>
  );
}

interface FormAlertProps {
  messages: ErrorMessages;
  variant?: AlertVariant;
  title?: string;
  dismissible?: boolean;
  onDismiss?: () => void;
  autoHide?: boolean;
  autoHideDelay?: number;
  className?: string;
  messagesStyles?: string;
  singleMessageStyles?: string;
  titleStyles?: string;
}

export function FormAlert({
  messages,
  variant = 'error',
  title = '',
  dismissible = true,
  onDismiss = () => {},
  autoHide = false,
  autoHideDelay = 5000,
  className = '',
  messagesStyles = '',
  singleMessageStyles = '',
  titleStyles = ''
}: FormAlertProps) {
  const [isVisible, setIsVisible] = useState(true);

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss();
  };

  const hasContent = hasMessageContent(messages);
  useAutoHide(autoHide, autoHideDelay, handleDismiss, hasContent);

  if (!isVisible || !hasContent) return null;

  return (
    <AlertContainer
      variant={variant}
      className={className}
      onDismiss={dismissible ? handleDismiss : undefined}
    >
      <AlertIcon />
      <div className="min-w-0 flex-1">
        {title && <h3 className={`text-xxs mb-2 ${titleStyles}`}>{title}</h3>}
        <MessageContent
          messages={messages}
          messagesStyles={messagesStyles}
          singleMessageStyles={singleMessageStyles}
        />
      </div>
      {dismissible && <DismissButton />}
    </AlertContainer>
  );
}
