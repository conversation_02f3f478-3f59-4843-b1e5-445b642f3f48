@import 'tailwindcss';
@source "../../../components";

@theme inline {
  --font-primary: 'Helvetica Neue', ui-sans-serif, system-ui, sans-serif;
}

/*::selection {*/
/*    background: #cbcccc;*/
/*    color: #00c2b3;*/

@theme {
  /* BREAKPOINTS */
  --breakpoint-custom: 1621px;
  --breakpoint-1200: 1200px;

  /* COLORS */
  --color-landing-page-background: #f6f6f6;
  --color-primary: #00ccbc;
  --color-text: #000;
  --color-purple-custom: #7125c7;
  --color-teal-custom: #00ccbb;
  --color-teal-light: #f3fcfc;
  --color-teal-darker: #c2e9e9;
  --color-heading: #141424;
  --color-jonas: #d6d6d6;

  --color-primary-hover: #c2e9e9;
  --color-tertiary: #f7f7f7;
  --color-secondary: #ffffff;
  --color-secondary-hover: #f3f3f4;

  --color-blueberry: #79c3ff;
  --color-blueberry-light: #bddcff;
  --color-blueberry-subtle: #e5f3ff;

  --color-mint: #50f094;
  --color-mint-light: #bff8d3;
  --color-mint-subtle: #e5fded;

  --color-lemon: #ffea70;
  --color-lemon-light: #fffac1;
  --color-lemon-subtle: #fffac1;

  --color-gray-subtle: #f3f3f3;
  --color-gray-subtle-tint: #1414141a;

  --color-border-subtle: #141414;
  --color-border: #d6d6d6;
  /*--color-border: #898989;*/
  --color-placeholder: #727272;
  --color-success-border: #00ab40;
  --color-warning: #fff6c1;
  --color-warning-border: #f9b728;
  --color-error: #ea0040;
  --color-error-subtle: #fee8ec;

  /* FONT WEIGHTS */
  --font-weight-heading: 700;
  --font-weight-body: 400;

  /* FONT SIZES */
  /* 1rem = 16px */
  --text-xxxs: 0.875rem; /* 14px */
  --text-default: 1rem; /* 16px */
  --text-xxs: 1.25rem; /* 20px */
  --text-xs: 1.5rem; /* 24px */
  --text-sm: 1.75rem; /* 28px */
  --text-base: 2.25rem; /* 36px */
  --text-lg: 2.5rem; /* 40px */
  --text-xl: 3rem; /* 48px */
  --text-2xl: 3.5rem; /* 56px */

  /* LINE HEIGHT */
  --line-height-xxs: 20px;
  --line-height-xs: 24px;
  --line-height-sm: 28px;
  --line-height-base: 32px;
  --line-height-large: 40px;
  --line-height-xl: 48px;
  --line-height-2xl: 56px;

  /* SPACING */
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-7: 32px;
  --spacing-8: 40px;
  --spacing-9: 48px;
  --spacing-10: 64px;
  --spacing-11: 80px;
  --spacing-12: 96px;
  --spacing-13: 104px;
  --spacing-14: 192px;
  --spacing-15: 300px;

  /* SHADOWS */
  --shadow-sm: 0px 1px 3px 1px rgba(20, 20, 20, 0.08),
    0px 1px 2px 0px rgba(20, 20, 20, 0.16);
  --shadow-md: 0px 1px 3px 0px rgba(20, 20, 20, 0.16),
    0px 4px 8px 3px rgba(20, 20, 20, 0.08);
  --shadow-lg: 0px 2px 3px 0px rgba(20, 20, 20, 0.16),
    0px 6px 10px 4px rgba(20, 20, 20, 0.08);
  --shadow-xl: 0px 4px 4px 0px rgba(20, 20, 20, 0.16),
    0px 8px 12px 6px rgba(20, 20, 20, 0.08);
  --shadow-none: none;

  /* PLAN SECTION SPACING */
  --plan-card-padding: var(--spacing-6);
  --plan-card-gap: var(--spacing-4);
}

@layer base {
  body {
    @apply text-text font-primary;
  }
}

@utility bg-custom-gradient {
  background: linear-gradient(258.78deg, #7125c7 -228.67%, #00ccbb 79.25%);
}

@utility bg-custom-gradient-header {
  position: relative;
  background: linear-gradient(256.05deg, #7125c7 21.82%, #43ccbc 73.8%);
  --mesh-opacity: 0.3;
}

.bg-custom-gradient-header::before {
  content: '';
  position: absolute;
  inset: 0;
  background: url('/images/linear-bg-mesh.png');
  background-size: cover;
  background-position: center;
  opacity: var(--mesh-opacity);
  pointer-events: none;
}

@layer components {
  .layout {
    @apply lg:bg-gray-subtle mx-auto px-[var(--spacing-6)] py-[var(--spacing-6)] lg:px-[var(--spacing-14)] lg:py-[var(--spacing-7)] xl:py-[var(--spacing-8)];
    /*TODO: use max w instead of padding*/
    /*@apply lg:bg-gray-subtle mx-auto px-[var(--spacing-6)] py-[var(--spacing-6)] lg:px-[var(--spacing-13)] lg:py-[var(--spacing-7)] xl:py-[var(--spacing-8)];*/
  }
  .layout-height {
    @apply min-h-[calc(100dvh-64px)];
  }
  .plain-card {
    @apply bg-secondary rounded-lg border-1 border-gray-300 p-2 lg:p-3;
    box-shadow:
      0 4px 8px 3px #14141414,
      0 1px 3px 0 #14141429;
  }
  .scroll-bg {
    background: #ffffffbf !important;
    backdrop-filter: blur(35px) !important;
  }
  .subscription-card-shadow {
    @apply shadow-[0px_4px_36.9px_0px_#00000033];
  }
  .backdrop-blur-custom {
    backdrop-filter: blur(8px);
  }
  .backdrop-blur-custom-desktop {
    backdrop-filter: blur(6px);
  }
  .mobile-backdrop-blur {
    backdrop-filter: blur(20px);
  }
  .hero-fixed-button-shadow {
    box-shadow: 0 -2px 10px 0 #0000001a;
  }
  .bg-login-image-mobile {
    background-image: url('../public/images/deliveroo-riders.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  .hero-image {
    background: linear-gradient(
        334.55deg,
        rgba(0, 0, 0, 0) 58.35%,
        rgba(0, 0, 0, 0.8) 75.31%
      ),
      url('../public/images/hero.webp');
    background-size: cover;
    background-position: 30%;
    background-repeat: no-repeat;
  }
  .carousel-card {
    box-shadow: 0 68px 60px 0 #00000029;
  }
  .card3 {
    background:
      linear-gradient(180deg, #000000 0%, rgba(0, 0, 0, 0) 39.22%),
      url('../public/images/rider-in-action1.webp') center;
  }
  .card2 {
    background:
      linear-gradient(180deg, #000000 0%, rgba(0, 0, 0, 0) 39.22%),
      url('../public/images/rider-in-action2.webp') left;
  }
  .card1 {
    background:
      linear-gradient(180deg, #000000 0%, rgba(0, 0, 0, 0) 39.22%),
      url('../public/images/rider-in-action3.webp') center;
  }
  .card1,
  .card2,
  .card3 {
    background-size: cover;
    background-repeat: no-repeat;
  }
  .glass-border {
    --card-bg: rgba(0, 0, 0, 0.4);
    border: 1px solid transparent;
    border-radius: 12px;
    background:
      linear-gradient(
          var(--card-bg, rgba(0, 0, 0, 0.4)),
          var(--card-bg, rgba(0, 0, 0, 0.4))
        )
        padding-box,
      linear-gradient(
          98.63deg,
          rgba(255, 255, 255, 0.1) 16.55%,
          rgba(255, 255, 255, 0) 135.38%
        )
        border-box;
    background-clip: padding-box, border-box;
  }
  .deliverooLink {
    @apply text-text cursor-pointer rounded-[1px] leading-6 font-[var(--font-primary)] underline underline-offset-2 hover:decoration-2 focus:outline-2 focus:outline-offset-4;
  }
  .outline {
    @apply focus:outline-none focus-visible:outline-4 focus-visible:outline-offset-3 focus-visible:outline-black focus-visible:outline-solid;
  }
  .chip {
    @apply text-xxxs bg-secondary border-gray-subtle-tint text-text w-fit rounded border p-[6px] font-bold;
  }
  .input {
    @apply bg-secondary border-border text-placeholder placeholder:text-default placeholder-placeholder w-full rounded-[2px] border p-3;
  }
  .plan-card-width-restriction {
    @apply w-[325px] xl:w-[372px];
  }
  .left-column {
    @apply order-2 flex w-full max-w-[850px] min-w-0 flex-col gap-4 lg:order-1;
  }
  .left-column-inner {
    @apply mb-36 p-6 pt-0 lg:mb-0 lg:pt-6;
  }
  .bottom-summary-wrapper {
    @apply bg-secondary border-gray-subtle-tint fixed right-0 bottom-0 left-0 border-t py-3 shadow-sm lg:static lg:border-none lg:px-0 lg:shadow-none;
  }
  .order-confirmation-layout {
    @media (min-width: 1024px) {
      padding-inline: 2vw;
    }

    @media (min-width: 1224px) {
      padding-inline: 10vw;
    }

    @media (min-width: 1620px) {
      padding-inline: 18vw;
    }

    @media (min-width: 1920px) {
      padding-inline: 23vw;
    }
  }
  details > summary {
    list-style: none;
  }

  details > summary::-webkit-details-marker {
    display: none;
  }

  details > summary::marker {
    display: none;
  }

  details[open] summary .chevron {
    transform: rotate(90deg);
  }
  .chevron {
    transition: transform 0.2s ease;
  }

  details .content {
    animation: accordion-down 0.2s ease-out;
  }

  details:not([open]) .content {
    animation: accordion-up 0.2s ease-out;
  }

  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.5);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes check-draw {
    from {
      stroke-dasharray: 24;
      stroke-dashoffset: 24;
    }
    to {
      stroke-dasharray: 24;
      stroke-dashoffset: 0;
    }
  }

  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-scale-in {
    animation: scale-in 0.6s ease-out forwards;
  }

  .animate-check-draw {
    animation: check-draw 0.8s ease-out 0.3s forwards;
    stroke-dasharray: 24;
    stroke-dashoffset: 24;
  }

  .animate-fade-in {
    animation: fade-in 0.8s ease-out 0.8s forwards;
    opacity: 0;
  }

  @media (min-width: 1024px) {
    .responsiveMaxWidth {
      max-width: clamp(320px, 83.33vw, 1920px);
    }
  }

  /* TYPOGRAPHY */
  p {
    font-family: var(--font-primary);
    font-size: var(--text-default);
    line-height: var(--line-height-xs);
    font-weight: var(--font-weight-body);
    color: var(--color-text);
    letter-spacing: 0;
    vertical-align: middle;
  }

  h1 {
    /*font-family: var(--font-heading);*/
    font-size: var(--text-lg);
    line-height: var(--line-height-large);
    color: var(--color-heading);
    font-weight: var(--font-weight-heading);
    letter-spacing: 0;
    vertical-align: middle;

    @media (width < 64rem) {
      font-size: var(--text-xs);
      line-height: var(--line-height-base);
    }
  }

  :focus-visible {
    outline: 1px solid black;
  }

  h2 {
    font-family: var(--font-heading);
    font-size: var(--text-xs);
    line-height: var(--line-height-base);
    font-weight: var(--font-weight-heading);
    color: var(--color-text);
    letter-spacing: 0;
    vertical-align: middle;
  }

  h3 {
    font-family: var(--font-heading);
    font-size: var(--text-xxs);
    line-height: var(--line-height-xs);
    font-weight: var(--font-weight-heading);
    color: var(--color-text);
    letter-spacing: 0;
    vertical-align: middle;
  }

  @keyframes fade-in-on-load {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  .fade-in {
    opacity: 0;
    animation: fade-in-on-load 800ms ease-out 120ms forwards;
  }

  @media (prefers-reduced-motion: reduce) {
    .fade-in {
      animation: none;
      opacity: 1;
    }
  }
}
