'use client';

import React from 'react';
import { ConditionalWrapper } from '@/components/conditional-wrapper/conditional-wrapper';
import Wrapper from '@/components/wrapper/wrapper';
import { usePlanSelection } from '@/src/deliveroo/app/signup/context/SignupContext';
import { LoadingSpinner } from '@/icons/icons';
import { getFieldError } from '@/utils/helpers';
import { Alert } from '@/components/alert/alert';
import { SignupProgressBar } from '@/src/deliveroo/app/signup/_components/signup-progress-bar/signup-progress-bar';
import { OrderSummary } from '@/src/deliveroo/app/signup/_components/order-summary/order-summary';
import Button from '@/components/button/button';
// import { useSignupProgress } from '@/context/signup-progress-context/signup-progress-tracked-routes';
import {
  SignupFormContainer,
  useSignUpFormSubmission
} from '../_components/register-form/register-form';
import { RequireDeliverooRiderAuth } from '@/components/require-auth/require-deliveroo-rider-auth';
import { MAX_MAIN_PLAN_QUANTITY } from '@/src/deliveroo/app/signup/_reducer/reducer';

const getErrorMap = (errors: string[]) => {
  return {
    name: getFieldError('name', errors),
    email: getFieldError('email', errors),
    password: getFieldError('password', errors)
  } as const;
};

export default function RegisterPage() {
  // const { updateProgress } = useSignupProgress();
  const { orderSummary } = usePlanSelection(MAX_MAIN_PLAN_QUANTITY);
  const {
    handleSubmit,
    isPending,
    errors,
    formData,
    setErrors,
    mutationError
  } = useSignUpFormSubmission();
  // } = useSignUpFormSubmission(updateProgress);

  const errorMap = getErrorMap(errors);

  const clearErrors = () => setErrors([]);

  return (
    <RequireDeliverooRiderAuth>
      <Wrapper>
        <div className="left-column">
          <ConditionalWrapper className="rounded border-gray-100 p-6 shadow-none lg:border">
            <SignupProgressBar />
            <h1 className="mb-4">User details</h1>
            <p >
              We need your name and email to set up your eSIM.
            </p>
            <SignupFormContainer
              handleSubmit={handleSubmit}
              errorMap={errorMap}
              clearErrors={clearErrors}
              formData={formData}
            >
              <Button
                disabled={isPending}
                form="signupForm"
                className="mt-4 w-full"
              >
                <div className="flex items-center justify-center gap-2">
                  {isPending && <LoadingSpinner />}
                  Continue
                </div>
              </Button>
              {mutationError && (
                <Alert
                  variant="error"
                  message={errors[0]}
                  className="mx-auto mt-4 w-fit"
                />
              )}
            </SignupFormContainer>
          </ConditionalWrapper>
        </div>
        <OrderSummary
          basketSummary={orderSummary.basketSummary}
          totalCost={orderSummary.totalCost}
        />
      </Wrapper>
    </RequireDeliverooRiderAuth>
  );
}
