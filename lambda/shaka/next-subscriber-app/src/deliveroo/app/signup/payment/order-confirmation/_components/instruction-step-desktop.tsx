import React from 'react';
import Image from 'next/image';
import gamma from '@/src/deliveroo/public/images/gamma.png';
import { infoGrid } from '@/src/deliveroo/utils/constants';

export function InstructionStepDesktop() {
  return (
    <>
      <div className="flex items-center justify-between">
        <h2 >Data settings</h2>
        <div className="flex w-fit gap-2 rounded bg-[#6421CD] p-2 font-bold text-white">
          <svg
            className="mt-[2px]"
            width="17"
            height="18"
            viewBox="0 0 17 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M12.5183 1.64614C12.8949 1.26957 12.8949 0.65901 12.5183 0.282433C12.1417 -0.0941443 11.5311 -0.0941443 11.1546 0.282433L9.226 2.21101C8.84941 2.58758 8.84941 3.19814 9.226 3.57471C9.60257 3.95128 10.2131 3.95128 10.5897 3.57471L12.5183 1.64614ZM10.5405 4.76154C11.1113 4.6531 11.6986 4.66631 12.264 4.80028C12.8294 4.93426 13.3602 5.186 13.8216 5.53905C14.2831 5.89211 14.6649 6.33853 14.9421 6.84918C15.0337 7.01786 15.0451 7.21861 14.9732 7.39657C14.9014 7.57453 14.7538 7.71107 14.5708 7.76889C14.0966 7.91863 13.6826 8.21536 13.3883 8.6161C13.0942 9.01684 12.9351 9.50081 12.9342 9.99797V10.0081C12.9243 10.5777 13.1232 11.1312 13.4936 11.564C13.8642 11.9968 14.3803 12.2788 14.9447 12.3568C15.126 12.3819 15.2881 12.4832 15.3902 12.6352C15.4924 12.7872 15.5248 12.9755 15.4794 13.1529C15.1036 14.6223 14.3428 15.9648 13.2754 17.0422C13.2657 17.0521 13.2554 17.0618 13.2448 17.0712C12.6939 17.559 11.9879 17.8356 11.2522 17.8522C10.5219 17.8687 9.80921 17.6279 9.23887 17.172C9.07893 17.0565 8.88662 16.9944 8.6892 16.9944C8.48902 16.9944 8.2941 17.0583 8.13285 17.1768C7.53868 17.6254 6.80472 17.8489 6.06128 17.8076C5.31641 17.7662 4.61045 17.4618 4.06923 16.9482C4.06075 16.9403 4.05249 16.9319 4.04447 16.9234C2.64655 15.4445 1.7705 13.5492 1.54941 11.5265C1.28179 10.0155 1.53371 8.45824 2.26437 7.1085C2.53841 6.57175 2.92106 6.09782 3.38809 5.71684C3.85742 5.33395 4.40159 5.05339 4.9857 4.89312C5.56981 4.73286 6.18097 4.69643 6.77998 4.78618C7.37358 4.87512 7.94225 5.08602 8.45023 5.40552C8.53251 5.45101 8.62504 5.47491 8.71917 5.47491C8.80975 5.47491 8.89886 5.45277 8.97877 5.41056C9.44892 5.0889 9.98063 4.86788 10.5405 4.76154Z"
              fill="white"
            />
          </svg>

          <span> iPhone only</span>
        </div>
      </div>
      <p className="my-6">
        If you have an iPhone, you need to modify your settings to be able to
        use data.
      </p>
      <div className="grid items-center gap-4 lg:grid-cols-[.5fr_1fr] lg:gap-8">
        <Image
          width={271}
          height={210}
          src={gamma}
          alt="Gamma setup instructions"
        />
        <ul className="text-xxs flex flex-col gap-2">
          {infoGrid.map((item) => {
            const Icon = item.icon;
            return (
              <li key={item.title} className="flex items-center gap-4">
                <div className="bg-[#C3E9E9] p-3">
                  <Icon className="h-7 w-7 flex-shrink-0" />
                </div>
                <div>
                  <p className="font-bold">{item.title}</p>
                  <p >{item.text}</p>
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </>
  );
}
