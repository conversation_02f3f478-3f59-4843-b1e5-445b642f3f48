import { ConditionalWrapper } from '@/components/conditional-wrapper/conditional-wrapper';
import { PlainCard } from '@/components/plain-card/plain-card';
import { Divider } from '@/components/divider/divider';
import { VideoInstructions } from '@/src/deliveroo/app/signup/payment/_components/video-instructions';
import { HelpSection } from '@/src/deliveroo/app/signup/payment/_components/help-section';
import React from 'react';
import { OrderConfirmationBanner } from './order-confirmation-banner';
import { MultipleInfoGridCore } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/multiple-info-grid-core';
import { InstructionStepsAndroid } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/instruction-steps-android';
import { TermsModalButton } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/terms-modal-button';
import { AccountAccessSection } from '../../_components/account-access';
import { checkIsAndroid, checkIsIos, checkIsSafari } from '@/utils/helpers';
import { QRCode } from '@/src/deliveroo/app/signup/payment/_components/qrcode';
import Button from '@/components/button/button';
import { Alert } from '@/components/alert/alert';
import { QrCodeSkeleton } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/qr-code-skeleton';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { AfterPaymentOrderSummary } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/after-payment-order-summary';
import { InstructionStepsIos } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/instruction-steps-iphone';
import { DataState } from '@/src/deliveroo/app/signup/payment/order-confirmation/types';
import { useSearchParams } from 'next/navigation';
import { usePurchaseIntentDetails } from '@/hooks/usePurchaseIntentDetails';
import { LoadingSpinner } from '@/icons/icons';

interface MultiPlanMobileProps {
  simTargetsWithStatus: any[];
  isPolling: boolean;
  error: Error | null;
  showFallbackMessage: boolean;
  dataState: DataState;
}

export function MultiPlanMobile({
  simTargetsWithStatus,
  isPolling,
  error,
  showFallbackMessage,
  dataState
}: MultiPlanMobileProps) {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id') || '';
  const {
    purchaseIntentDetails,
    isLoading: isPurchaseIntentLoading,
    isError: isPurchaseIntentError
  } = usePurchaseIntentDetails(sessionId);

  const totalCost =
    purchaseIntentDetails?.order_summary.total_cost.toString() || '';
  const basketSummary = purchaseIntentDetails?.order_summary.items || [];

  let MultiEsimInstruction: React.ComponentType<EsimInstructions>;

  if (checkIsIos() && checkIsSafari()) {
    MultiEsimInstruction = IphoneSafariMultiEsimInstructions;
  } else if (checkIsIos()) {
    MultiEsimInstruction = IphoneMultiEsimInstructions;
  } else if (checkIsAndroid()) {
    MultiEsimInstruction = AndroidMultipleEsimInstructions;
  } else {
    MultiEsimInstruction = AndroidMultipleEsimInstructions;
  }

  return (
    <ConditionalWrapper className="px-6 pt-6 lg:p-6">
      <OrderConfirmationBanner dataState={dataState} />
      <h2 data-testid="multiple-esim-instructions-mobile">Your eSIM QR code</h2>
      <p className="mb-5">
        We&apos;ll also email you your eSIM if you would like to do this later.
      </p>
      <div className="pb-4">
        {showFallbackMessage && (
          <div>
            <FormAlert
              variant="info"
              messages={[
                'message: We could not generate QR code at this time. We will email them to you'
              ]}
              dismissible={false}
            />
          </div>
        )}
        {!showFallbackMessage && (
          <PlainCard className="mb-4 px-0 py-4">
            {error && (
              <Alert
                className="w-fit p-6"
                message={'We could not load plan details. Please try again'}
              />
            )}
            {MultiEsimInstruction &&
              simTargetsWithStatus?.map((details: any, index: number) => {
                const totalPlans = basketSummary.length;
                const isFirstItem = index === 0;
                const isLastItem = index === totalPlans - 1;
                const showTopMargin = index > 0;
                const showBottomBorder = !isLastItem;

                const containerClasses = [
                  showTopMargin ? 'mt-2' : '',
                  showBottomBorder ? 'border-b border-gray-300' : ''
                ]
                  .filter(Boolean)
                  .join(' ');

                return (
                  <div
                    key={`plan-${details.target.correlation_id}`}
                    className="px-3 pb-4"
                  >
                    <MultipleInfoGridCore
                      isPolling={isPolling}
                      details={details}
                    />
                    <MultiEsimInstruction
                      isPolling={isPolling}
                      isFirstItem={isFirstItem}
                      containerClasses={containerClasses}
                    />
                  </div>
                );
              })}
          </PlainCard>
        )}

        <Divider className="my-6" />
        <VideoInstructions />
        <Divider className="my-6" />
        <AccountAccessSection />
        <Divider className="my-6" />

        <h2 className="my-6">Need some help?</h2>
        <HelpSection />
        <Divider className="my-6" />
        {isPurchaseIntentError && (
          <Alert
            variant="error"
            message="We could not load your order summary."
          />
        )}
        {isPurchaseIntentLoading && !isPurchaseIntentError ? (
          <LoadingSpinner />
        ) : (
          <AfterPaymentOrderSummary
            basketSummary={basketSummary}
            totalCost={totalCost}
          />
        )}
        <Divider />
        <div className="my-4">
          <p className="text-xxxs inline">Full </p>
          <TermsModalButton />
          <p className="text-xxxs inline"> apply.</p>
        </div>
      </div>
    </ConditionalWrapper>
  );
}

interface EsimInstructions {
  isFirstItem: boolean;
  isPolling: boolean;
  containerClasses: string;
}

function AndroidMultipleEsimInstructions({
  isFirstItem,
  containerClasses
}: EsimInstructions) {
  return (
    <div
      data-testid="multi-esim-instructions-android"
      className={containerClasses}
    >
      {isFirstItem && (
        <>
          <Divider className="my-4" />
          <InstructionStepsAndroid />
        </>
      )}
    </div>
  );
}

function IphoneMultiEsimInstructions({
  isFirstItem,
  isPolling,
  containerClasses
}: EsimInstructions) {
  return (
    <div data-testid="multi-esim-instructions-ios" className={containerClasses}>
      {isFirstItem && (
        <>
          <Divider className="my-4" />
          <InstructionStepsIos />
          {isPolling ? (
            <QrCodeSkeleton
              className="mx-auto my-6 aspect-square h-full! max-h-[400px] w-full! max-w-[400px]"
              size="max"
            />
          ) : (
            <QRCode
              className="mx-auto my-6 aspect-square h-full! max-h-[400px] w-full! max-w-[400px]"
              qrcode="asd"
              size="max"
            />
          )}
        </>
      )}
    </div>
  );
}

function IphoneSafariMultiEsimInstructions({
  isFirstItem,
  isPolling,
  containerClasses
}: EsimInstructions) {
  // ideally a hook with logic to install it ?
  return (
    <div
      data-testid="multi-esim-instructions-ios-safari"
      className={containerClasses}
    >
      {isFirstItem && (
        <>
          <Divider className="my-4" />
          {isPolling ? (
            <div className="mb-4 h-9 w-full animate-pulse rounded-lg bg-gray-200"></div>
          ) : (
            <Button variant="primary" className="mb-4 w-full">
              Install eSIM
            </Button>
          )}
        </>
      )}
    </div>
  );
}
