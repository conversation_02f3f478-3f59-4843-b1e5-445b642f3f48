import React, { Fragment } from 'react';
import { Divider } from '@/components/divider/divider';
import {
  OrderSummaryRow,
  TotalCostAside
} from '@/src/deliveroo/app/signup/_components/order-summary/order-summary';
import type { PurchaseIntentOrderItem } from '@/services/paymentService';

interface AfterPaymentOrderSummaryProps {
  basketSummary: PurchaseIntentOrderItem[];
  totalCost: string;
}

export function AfterPaymentOrderSummary({
  basketSummary,
  totalCost
}: AfterPaymentOrderSummaryProps) {
  return (
    <div data-testid="order-summary">
      <h3 >Order summary</h3>
      <ol className="mt-6 space-y-4">
        {basketSummary?.map((item) => {
          return (
            <Fragment key={item.id}>
              <OrderSummaryRow
                planName={item.name}
                planPrice={+item.price}
                quantity={item.quantity}
              />
            </Fragment>
          );
        })}
      </ol>
      <br />
      <Divider className="mt-0" />
      <TotalCostAside totalCost={totalCost} />
      <br />
      <footer
        aria-label="Payment details"
        className="flex items-end justify-between"
      >
        <div className="flex w-full flex-col items-end">
          <dl className="flex w-full items-end justify-between">
            <dt className="text-default">Payment details</dt>
            <dd className="text-default font-bold">
              <strong>Card payment</strong>
            </dd>
          </dl>
        </div>
      </footer>
    </div>
  );
}
