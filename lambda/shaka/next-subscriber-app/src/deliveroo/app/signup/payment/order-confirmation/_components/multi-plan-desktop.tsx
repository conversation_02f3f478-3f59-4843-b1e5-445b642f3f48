import { ConditionalWrapper } from '@/components/conditional-wrapper/conditional-wrapper';
import {
  InfoGrid,
  MultipleInfoGridItem
} from '@/src/deliveroo/app/signup/payment/_components/info-grid';
import { PlainCard } from '@/components/plain-card/plain-card';
import { Divider } from '@/components/divider/divider';
import { VideoInstructions } from '@/src/deliveroo/app/signup/payment/_components/video-instructions';
import { HelpSection } from '@/src/deliveroo/app/signup/payment/_components/help-section';
import React from 'react';
import { TermsModalButton } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/terms-modal-button';
import { AccountAccessSection } from '@/src/deliveroo/app/signup/payment/_components/account-access';
import { Alert } from '@/components/alert/alert';
import { InstructionBaseWrapper } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/instruction-base-wrapper';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { AfterPaymentOrderSummary } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/after-payment-order-summary';
import { DataState } from '@/src/deliveroo/app/signup/payment/order-confirmation/types';
import { usePurchaseIntentDetails } from '@/hooks/usePurchaseIntentDetails';
import { useSearchParams } from 'next/navigation';
import { LoadingSpinner } from '@/icons/icons';

interface MultiPlanDesktopProps {
  simTargetsWithStatus: any[];
  isPolling: boolean;
  error: Error | null;
  showFallbackMessage: boolean;
  dataState: DataState;
}

export function MultiPlanDesktop({
  simTargetsWithStatus,
  isPolling,
  error,
  showFallbackMessage,
  dataState
}: MultiPlanDesktopProps) {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id') || '';
  const {
    purchaseIntentDetails,
    isLoading: isPurchaseIntentLoading,
    isError: isPurchaseIntentError
  } = usePurchaseIntentDetails(sessionId);

  const totalCost =
    purchaseIntentDetails?.order_summary.total_cost.toString() || '';
  const basketSummary = purchaseIntentDetails?.order_summary.items || [];

  return (
    <>
      <ConditionalWrapper className="rounded border-none px-6 pt-6 shadow-none lg:p-6">
        <InstructionBaseWrapper dataState={dataState} type="desktop-multiple">
          <InfoGrid row />
          <PlainCard className="mt-5 p-0">
            {error && (
              <Alert
                className="w-fit p-6"
                message={'We could not generate qr code.'}
              />
            )}
            {showFallbackMessage && (
              <div>
                <FormAlert
                  variant="info"
                  messages={[
                    'message: We could not generate QR code at this time. We will email them to you'
                  ]}
                  dismissible={false}
                />
              </div>
            )}
            {!showFallbackMessage &&
              simTargetsWithStatus?.map((details: any) => {
                return (
                  <MultipleInfoGridItem
                    key={details.target.correlation_id}
                    isPolling={isPolling}
                    details={details}
                  />
                );
              })}
          </PlainCard>
          <Divider className="my-6" />
          <VideoInstructions />
        </InstructionBaseWrapper>
      </ConditionalWrapper>

      <ConditionalWrapper className="rounded border-none px-6 pt-6 shadow-none lg:p-6">
        <AccountAccessSection />
      </ConditionalWrapper>
      <ConditionalWrapper className="rounded border-none px-6 pt-6 shadow-none lg:p-6">
        <h2 className="mb-6">Need some help?</h2>
        <HelpSection />
      </ConditionalWrapper>
      <ConditionalWrapper className="rounded border-none px-6 pt-6 shadow-none lg:p-6">
        {isPurchaseIntentError && (
          <Alert
            variant="error"
            message="We could not load your order summary."
          />
        )}
        {isPurchaseIntentLoading && !isPurchaseIntentError ? (
          <LoadingSpinner />
        ) : (
          <AfterPaymentOrderSummary
            basketSummary={basketSummary}
            totalCost={totalCost}
          />
        )}
        <Divider />
        <div className="my-4">
          <p className="text-xxxs inline">Full </p>
          <TermsModalButton />
          <p className="text-xxxs inline"> apply.</p>
        </div>
      </ConditionalWrapper>
    </>
  );
}
