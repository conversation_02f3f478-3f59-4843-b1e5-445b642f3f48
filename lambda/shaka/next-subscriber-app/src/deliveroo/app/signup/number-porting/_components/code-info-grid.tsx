import {
  CloseIcon,
  PACIcon,
  PlayButtonIcon,
  TextMessageIcon
} from '@/icons/icons';
import Image from 'next/image';
import PACIphone from '@/public/images/PAC-iphone.png';
import StackIphone from '@/public/images/STAC-iphone.png';
import { FullDetailsButton } from '@/components/full-details-button/full-details-button';
import Modal from '@/components/modal/modal';
import React from 'react';

export function CodeInfoGrid() {
  return (
    <>
      <div className="mt-6 mb-4 grid gap-6 lg:grid-cols-[1fr_auto] lg:gap-4">
        <div className="space-y-6">
          <CodeCard
            icon={<TextMessageIcon fill="#C2E9E9" />}
            title="Text PAC to 65075"
          >
            This notifies your current provider you would like to switch away
            from them and keep your number.
          </CodeCard>

          <CodeCard
            icon={<PACIcon fill="#C2E9E9" />}
            title="Enter your code below"
          />

          <div>
            <PlayButtonIcon className="mr-2 inline align-middle" />
            <FullDetailsButton
              className="deliverooLink"
              text="Watch an instructional video"
            >
              {({ isOpen, setIsOpen }) =>
                isOpen && (
                  <Modal onOpenChange={setIsOpen} open={isOpen}>
                    <Modal.Overlay />
                    <Modal.Content className="w-full rounded-lg p-6 lg:max-w-2xl">
                      <div className="mb-4 flex justify-end">
                        <Modal.Close>
                          <CloseIcon />
                        </Modal.Close>
                      </div>
                      <Modal.Title className="mb-6 text-xl font-semibold">
                        {/*conditioanlly rendered insturction here*/}
                        <p>hi</p>
                      </Modal.Title>
                      <Modal.Description>
                        <p>hello</p>
                      </Modal.Description>
                    </Modal.Content>
                  </Modal>
                )
              }
            </FullDetailsButton>
          </div>
        </div>

        <div className="flex justify-center lg:justify-end">
          <Image
            priority
            className="w-full lg:-mt-6 lg:max-w-[271px]"
            src={PACIphone}
            alt="Number porting - PAC"
            width={271}
            height={210}
          />
        </div>
      </div>
    </>
  );
}

export function StackCodeInfoGrid() {
  return (
    <>
      <div className="mt-6 mb-4 grid gap-6 lg:grid-cols-[1fr_auto]">
        <div className="space-y-6">
          <CodeCard
            icon={<TextMessageIcon fill="#C2E9E9" />}
            title="Text STAC to 75075"
          >
            This notifies your current provider you would like to switch away
            from them.
          </CodeCard>

          <CodeCard
            icon={<PACIcon fill="#C2E9E9" />}
            title="Enter your code below"
          />

          <div>
            <PlayButtonIcon className="mr-2 inline align-middle" />
            <FullDetailsButton
              className="deliverooLink"
              text="Watch an instructional video"
            >
              {({ isOpen, setIsOpen }) =>
                isOpen && (
                  <Modal onOpenChange={setIsOpen} open={isOpen}>
                    <Modal.Overlay />
                    <Modal.Content className="w-full rounded-lg p-6 lg:max-w-2xl">
                      <div className="mb-4 flex justify-end">
                        <Modal.Close>
                          <CloseIcon />
                        </Modal.Close>
                      </div>
                      <Modal.Title className="mb-6 text-xl font-semibold">
                        <p>hi</p>
                      </Modal.Title>
                      <Modal.Description>
                        <p>hello</p>
                      </Modal.Description>
                    </Modal.Content>
                  </Modal>
                )
              }
            </FullDetailsButton>
          </div>
        </div>

        <div className="flex justify-center lg:justify-end">
          <Image
            priority
            className="w-full lg:-mt-6 lg:max-w-[271px]"
            src={StackIphone}
            alt="Number porting - stack"
            width={271}
            height={210}
          />
        </div>
      </div>
    </>
  );
}

interface CodeCardProps extends React.PropsWithChildren {
  icon: React.ReactNode;
  title: string;
  className?: string;
}

export function CodeCard({
  icon,
  title,
  children,
  className = ''
}: CodeCardProps) {
  return (
    <div className={`grid grid-cols-[auto_1fr] items-start gap-6 ${className}`}>
      {icon}
      <div>
        <h2 className="text-[18px] font-normal">{title}</h2>
        <p className="text-balanced">{children}</p>
      </div>
    </div>
  );
}
