'use client';

import React from 'react';
import { Divider } from '@/components/divider/divider';
import { BasketSummaryItem } from '@/src/deliveroo/utils/helpers';
import { PACStateManager } from '@/hooks/usePACStateManager';
import Button from '@/components/button/button';

interface OrderItemDetailsProps {
  item: BasketSummaryItem & { instanceId: string };
}

function OrderItemDetails({ item }: OrderItemDetailsProps) {
  return (
    <div aria-hidden="true">
      <div className="flex flex-wrap items-end">
        <p className="leading-xs mr-1 text-sm leading-none font-bold">
          {item.displayName}
        </p>
        <p className="text-default leading-none">SIM plan</p>
      </div>
      <div className="flex flex-col">
        <p className="text-xxxs font-bold">
          {item.europeData}GB roaming in Europe
        </p>
      </div>
    </div>
  );
}

interface PlanItemWithPACProps {
  item: BasketSummaryItem & { instanceId: string };
  pacStateManager: PACStateManager;
}

export function PlanItemWithPAC({
  item,
  pacStateManager
}: PlanItemWithPACProps) {
  const { openModal, hasEnteredPAC, getPacData } = pacStateManager;
  const pacData = getPacData(item.instanceId);
  const canRenderPACDetails = hasEnteredPAC(item.instanceId);

  const handleEnterPAC = () => {
    openModal(item.instanceId);
  };

  // const handleEditPAC = () => {
  //   openModal(item.instanceId);
  // };

  return (
    <div
      className="border-gray-subtle-tint w-full border-b px-3 py-4 last:border-b-0"
      data-testid="plan-item-with-pac"
    >
      <div
        className={`flex w-full ${canRenderPACDetails ? 'flex-col' : ''} justify-between border border-none p-0 text-left lg:flex-row lg:items-center`}
      >
        <div className="flex-1">
          <OrderItemDetails item={item} />
        </div>
        {canRenderPACDetails && <Divider className="lg:hidden" />}
        <div className="flex flex-col gap-2">
          {canRenderPACDetails ? (
            <div >
              <div className="lg:text-right">
                <div className="mb-1">
                  <span className="font-semibold">PAC code: </span>
                  <span>{pacData?.pacCode}</span>
                </div>
                <div className="mb-2">
                  <span className="font-semibold">Your phone number: </span>
                  <span>{pacData?.incoming_phone_number}</span>
                </div>

                <div className="flex items-center gap-2 lg:justify-end">
                  {/*  <Button*/}
                  {/*    onClick={handleEditPAC}*/}
                  {/*    variant="link"*/}
                  {/*    className="w-fit p-0!"*/}
                  {/*  >*/}
                  {/*    Edit*/}
                  {/*    <EditIcon />*/}
                  {/*  </Button>*/}
                  <div className="bg-warning-border h-2 w-2 rounded-full"></div>
                  <span className="animate-pulse">
                    PAC request in progress...
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <Button
              onClick={handleEnterPAC}
              variant="secondary"
              className="min-w-[120px]"
              data-testid="open-pac-modal"
            >
              Enter PAC
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
