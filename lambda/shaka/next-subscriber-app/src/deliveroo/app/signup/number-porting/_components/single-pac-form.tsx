'use client';

import React from 'react';
import { NumberPortingFormContainer } from '@/components/number-porting-form/number-porting-form';
import { Divider } from '@/components/divider/divider';
import { NextStepLink } from '@/components/cta-button/next-step-link';
import Button from '@/components/button/button';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { usePACFormSubmission } from '@/hooks/usePACFormSubmission';
import useGetCurrentlySelectedPlan from '@/hooks/useGetCurrentlySelectedPlan';

export function SinglePacForm() {
  const currentSubscription = useGetCurrentlySelectedPlan();
  const {
    handleSubmit,
    isPending: submittingPacForm,
    errors,
    formData,
    setErrors,
    successMessage
  } = usePACFormSubmission(currentSubscription?.id);

  return (
    <>
      <NumberPortingFormContainer
        setErrors={setErrors}
        errors={errors}
        handleSubmit={handleSubmit}
        formData={formData}
      />

      <Divider />

      {/*DESKTOP BUTTONS*/}
      <div className="flex gap-4">
        <NextStepLink
          variant="secondary"
          className="hidden! border-black! hover:text-black! lg:inline-block!"
          text="Skip for now"
          href={{
            pathname: ROUTES_CONFIG['dashboard-esim'].path
          }}
        />
        <Button
          isLoading={submittingPacForm}
          aria-disabled={submittingPacForm}
          disabled={submittingPacForm}
          form="pacForm"
          variant="primary"
          className="hidden w-full cursor-pointer lg:inline-block"
        >
          Continue
        </Button>
      </div>

      {successMessage && (
        <FormAlert
          className="mb-4 lg:my-6"
          title=""
          variant="success"
          messages={[
            'success: Your PAC code has been successfully sent. You will be redirected to the dashboard.'
          ]}
          dismissible={false}
        />
      )}

      {/*TODO: establish what backend return in case of network error and then adjust accordingly*/}
      {/*{errors.length > 0 && (*/}
      {/*  <FormAlert*/}
      {/*    className="mb-4 lg:my-6"*/}
      {/*    title=""*/}
      {/*    variant="error"*/}
      {/*    messages={['message: Something went wrong, please try again later.']}*/}
      {/*    dismissible={false}*/}
      {/*  />*/}
      {/*)}*/}

      {/*MOBILE BUTTONS*/}
      <div className="flex gap-4 pb-3">
        <NextStepLink
          disabled={submittingPacForm}
          className="inline-block flex-1 lg:hidden"
          variant="secondary"
          text="Skip for now"
          href={{
            pathname: ROUTES_CONFIG['dashboard-esim'].path
          }}
        />
        <Button
          variant="primary"
          className="flex-1 lg:hidden"
          isLoading={submittingPacForm}
          aria-disabled={submittingPacForm}
          disabled={submittingPacForm}
          form="pacForm"
        >
          Continue
        </Button>
      </div>
    </>
  );
}
