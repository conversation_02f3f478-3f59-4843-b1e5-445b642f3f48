'use client';

import React from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import { NextStepLink } from '@/components/cta-button/next-step-link';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { BasketSummaryItem } from '@/src/deliveroo/utils/helpers';
import { PlanItemWithPAC } from './plan-item-with-pac';
import { PacFormModal } from './pac-form-modal';
import { usePACStateManager } from '@/hooks/usePACStateManager';

interface MultiPacFormProps {
  basketItemList: (BasketSummaryItem & { instanceId: string })[];
}

export function MultiPacForm({ basketItemList }: MultiPacFormProps) {
  const pacStateManager = usePACStateManager();
  return (
    <>
      <PlainCard className="mb-4 p-0">
        {basketItemList.map((item) => (
          <PlanItemWithPAC
            key={item.instanceId}
            item={item}
            pacStateManager={pacStateManager}
          />
        ))}
      </PlainCard>

      <div className="flex gap-4">
        <NextStepLink
          variant="primary"
          text="Skip for now"
          href={{
            pathname: ROUTES_CONFIG['dashboard-esim'].path
          }}
        />
        <NextStepLink
          variant="secondary"
          text="Continue"
          className="hidden! border-black! hover:text-black! lg:inline-block!"
          href={{
            pathname: ROUTES_CONFIG['dashboard-esim'].path
          }}
        />
      </div>
      <PacFormModal
        pacStateManager={pacStateManager}
        basketItemList={basketItemList}
      />
    </>
  );
}
