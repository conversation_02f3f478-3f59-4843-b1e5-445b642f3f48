import { PlainCard } from '@/components/plain-card/plain-card';
import { Divider } from '@/components/divider/divider';

export function UserDetailsCard() {
  // api call via hook here
  return (
    <PlainCard as="article" className="rounded border-none shadow-none">
      <header>
        <h3>Alex Bloggs</h3>
      </header>
      <Divider className="mt-2" />
      <div className="mt-5 space-y-2">
        <p><EMAIL></p>
        <p>Rider Id: 12312</p>
      </div>
    </PlainCard>
  );
}
