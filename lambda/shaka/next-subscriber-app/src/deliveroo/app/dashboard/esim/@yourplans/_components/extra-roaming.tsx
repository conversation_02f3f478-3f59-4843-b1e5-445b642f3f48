import React, { useState } from 'react';
import { TermsConditions } from '@/components/terms-conditions/terms-conditions';
import { Divider } from '@/components/divider/divider';
import Button from '@/components/button/button';
import { useBuyExtraRoaming } from '@/hooks/useBuyExtraRoaming';
import { Alert } from '@/components/alert/alert';
import { ErrorMessages, FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { useCurrentPlanId } from '@/context/current-plan-context';

// comes from api
const roamingDataOptions = [
  { label: '1GB', value: 1 },
  { label: '2GB', value: 2 },
  { label: '3GB', value: 3 },
  { label: '10GB', value: 5 }
] as const;

  type RoamingDataOption = (typeof roamingDataOptions)[number];

export function ExtraRoaming() {
  const currentPlanId = useCurrentPlanId();
  const [selectedRoamingOption, setSelectedRoamingOption] =
    useState<RoamingDataOption>(roamingDataOptions[0]);

  const {
    buyExtraRoamingData,
    isPending,
    error: mutationError,
    isSuccess
  } = useBuyExtraRoaming();

  const handleConfirm = () => {
    if (!currentPlanId) return;
    buyExtraRoamingData({
      planId: currentPlanId,
      roamingData: selectedRoamingOption.value
    });
  };

  return (
    <>
      <IntroText  />

      <RoamingOptionsList
        options={roamingDataOptions}
        selectedLabel={selectedRoamingOption.label}
        onSelect={setSelectedRoamingOption}
        selectedClassName="border-text outline-2"
      />

      <Divider className="my-4" />

      <PurchaseNotice className="text-xxxs mb-2 text-center" />

      <ExtraRoamingActionButton isPending={isPending} onConfirm={handleConfirm} />

      <ExtraRoamingErrorAlert error={mutationError} />

      {isSuccess && (
        <ExtraRoamingSuccessNotice
          className="my-6 mb-4"
          title=""
          variant="success"
          messages={[
            `message: Your extra roaming data has been successfully added. You may now close this window.`
          ]}
          dismissible={false}
          singleMessageStyles=""
        />
      )}

      <ExtraRoamingTerms termsClassName="deliverooLink" />
    </>
  );
}

// Presentational subcomponents
function IntroText({ className }: { className?: string }) {
  return <p className={className}>Get extra EU data to use right away.</p>;
}

interface RoamingOptionsListProps {
  options: ReadonlyArray<RoamingDataOption>;
  selectedLabel: string;
  onSelect: (option: RoamingDataOption) => void;
  selectedClassName?: string;
  itemClassNameBase?: string;
}

function RoamingOptionsList({
  options,
  selectedLabel,
  onSelect,
  selectedClassName,
  itemClassNameBase
}: RoamingOptionsListProps) {
  return (
    <div className="mt-3 grid grid-cols-1 gap-2 lg:grid-cols-2">
      {options.map((option) => (
        <RoamingOptionItem
          key={option.label}
          option={option}
          selected={selectedLabel === option.label}
          onSelect={() => onSelect(option)}
          classNameBase={itemClassNameBase}
          selectedClassName={selectedClassName}
        />
      ))}
    </div>
  );
}

interface RoamingOptionItemProps {
  option: RoamingDataOption;
  selected: boolean;
  onSelect: () => void;
  classNameBase?: string;
  selectedClassName?: string;
}

function RoamingOptionItem({
  option,
  selected,
  onSelect,
  classNameBase = 'hover:bg-gray-subtle flex w-full cursor-pointer justify-between rounded border px-4 py-2',
  selectedClassName = ''
}: RoamingOptionItemProps) {
  const className = `${classNameBase} ${selected ? selectedClassName : ''}`;
  return (
    <button className={className} onClick={onSelect} type="button">
      <span className="font-semibold">{option.label} data</span>
      <span>£{option.value}</span>
    </button>
  );
}

function PurchaseNotice({ className }: { className?: string }) {
  return (
    <p className={className ?? 'text-xxxs mb-2 text-center'}>
      We’ll add the charge to your next bill.
    </p>
  );
}

interface ExtraRoamingActionButtonProps {
  isPending: boolean;
  onConfirm: () => void;
  className?: string;
}

function ExtraRoamingActionButton({
  isPending,
  onConfirm,
  className
}: ExtraRoamingActionButtonProps) {
  return (
    <Button
      onClick={onConfirm}
      variant="primary"
      className={className ?? 'w-full'}
      isLoading={isPending}
      disabled={isPending}
    >
      Buy now
    </Button>
  );
}

function ExtraRoamingErrorAlert({ error }: { error: Error | null }) {
  if (!error?.message) return null;
  return (
    <Alert
      variant="error"
      message={error.message}
      align="left"
      className="my-4"
    />
  );
}

interface ExtraRoamingSuccessNoticeProps {
  messages: ErrorMessages;
  title?: string;
  className?: string;
  dismissible?: boolean;
  titleStyles?: string;
  messagesStyles?: string;
  singleMessageStyles?: string;
  variant?: 'success' | 'error' | 'info' | 'warning';
}

function ExtraRoamingSuccessNotice({
  messages,
  title = '',
  className,
  dismissible = false,
  titleStyles,
  messagesStyles,
  singleMessageStyles,
  variant = 'success'
}: ExtraRoamingSuccessNoticeProps) {
  return (
    <FormAlert
      className={className ?? 'my-6 mb-4'}
      title={title}
      variant={variant}
      messages={messages}
      dismissible={dismissible}
      titleStyles={titleStyles}
      messagesStyles={messagesStyles}
      singleMessageStyles={singleMessageStyles}
    />
  );
}

function ExtraRoamingTerms({
  wrapperClassName,
  termsClassName
}: {
  wrapperClassName?: string;
  termsClassName?: string;
}) {
  return (
    <div className={wrapperClassName ?? 'mt-3'}>
      <TermsConditions className={termsClassName} />
    </div>
  );
}
