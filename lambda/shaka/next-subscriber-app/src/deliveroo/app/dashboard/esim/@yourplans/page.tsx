'use client';

import { PlanDetailsCarousel } from '@/components/plan-card-carousel/plan-card-carousel';
import { PlainCard } from '@/components/plain-card/plain-card';
import { Divider } from '@/components/divider/divider';
import { FullDetailsButton } from '@/components/full-details-button/full-details-button';
import Modal from '@/components/modal/modal';
import { CloseIcon } from '@/icons/icons';
import React, { useState } from 'react';
import { CancelPlan } from '@/src/deliveroo/app/dashboard/esim/@yourplans/_components/cancel-plan';
import { KeepYourNumber } from '@/src/deliveroo/app/dashboard/esim/@yourplans/_components/keep-your-number';
import { LabelPlan } from '@/src/deliveroo/app/dashboard/esim/@yourplans/_components/label-plan';
import { ESIMInstructions } from '@/src/deliveroo/app/dashboard/esim/@yourplans/_components/esim-instructions';
import { Alert } from '@/components/alert/alert';
import { useSearchParams } from 'next/navigation';
import { YourPlansLoadingSkeleton } from '@/src/deliveroo/app/dashboard/esim/@yourplans/_components/your-plans-skeleton';
import { AccountBillingSkeleton } from '@/src/deliveroo/app/dashboard/esim/@yourplans/_components/account-billing-skeleton';
import { HelpSectionSkeleton } from '@/src/deliveroo/app/dashboard/esim/@yourplans/_components/help-section-skeleton';
import { CurrentPlanProvider } from '@/context/current-plan-context';
import { useSubscription } from '@/hooks/useSubscription';
import { unlimitedPlanPerks } from '@/utils/constants';
import { tabConfig } from '@/src/deliveroo/utils/constants';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import Link from 'next/link';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { GenericModal } from '@/components/generic-modal/generic-modal';
import { useUrlParamModal } from '@/hooks/useParamModal';
import useGetCurrentlySelectedPlan from '@/hooks/useGetCurrentlySelectedPlan';
import { filterActionGroups } from '@/src/deliveroo/utils/helpers';

const tabFallbacks = {
  'your-plans': <YourPlansLoadingSkeleton />,
  'account-billing': <AccountBillingSkeleton />,
  help: <HelpSectionSkeleton />
};

export default function YourPlans() {
  const searchParams = useSearchParams();
  const activeTab = searchParams.get('tab') || tabConfig[0].value;
  const [isKeepNumberModalOpen, setIsKeepNumberModalOpen] = useState(false);

  const { subscriptions, loadingSubscriptions, subscriptionsError } =
    useSubscription();

  if (subscriptionsError) {
    return (
      <YourPlansErrorBoundary
        subscriberError={null}
        subscriptionsError={subscriptionsError}
      />
    );
  }

  if (loadingSubscriptions) {
    return (
      <YourPlansLoadingState
        isLoading={loadingSubscriptions}
        activeTab={activeTab}
      />
    );
  }

  if (!subscriptions?.length) {
    return (
      <>
        <FormAlert
          className="mt-4"
          title="No subscriptions found"
          variant="info"
          titleStyles=""
          singleMessageStyles=""
          messages={[
            `error: Please click on the link below and go through the onboarding process to create a subscription.`
          ]}
        />
        <Link
          className="text-md mt-4 block text-center underline"
          href={ROUTES_CONFIG['plan-selection'].path}
        >
          Plan selection page
        </Link>
      </>
    );
  }

  const handleKeepNumberClick = () => setIsKeepNumberModalOpen(true);
  const handleModalOpenChange = (open: boolean) =>
    setIsKeepNumberModalOpen(open);

  return (
    <AdditionalPurchaseGuard>
      <CurrentPlanProvider>
        <YourPlansLayout>
          <PlanDetailsCarousel
            subscriptions={subscriptions}
            onKeepNumberClick={handleKeepNumberClick}
            classes={planDetailsCarouselStyles}
          />
          <PlanManagementPanel />
        </YourPlansLayout>
        <KeepNumberModal
          isOpen={isKeepNumberModalOpen}
          onOpenChange={handleModalOpenChange}
        />
      </CurrentPlanProvider>
    </AdditionalPurchaseGuard>
  );
}

function YourPlansErrorBoundary({ subscriberError, subscriptionsError }) {
  if (subscriberError) {
    return (
      <Alert message="We could not load your subscriber details. Please try again later." />
    );
  }

  if (subscriptionsError) {
    return (
      <Alert message="We could not load your subscriptions details. Please try again later." />
    );
  }

  return null;
}

function YourPlansLoadingState({ isLoading, activeTab }) {
  if (!isLoading) return null;
  return tabFallbacks[activeTab];
}

function YourPlansLayout({ children }) {
  return (
    <section className="grid grid-cols-1 gap-y-8 md:gap-x-6 md:gap-y-0 lg:grid-cols-[minmax(0,1.3fr)_minmax(0,0.7fr)] lg:gap-x-8 xl:grid-cols-[minmax(0,1fr)_minmax(0,0.6fr)] xl:gap-x-2">
      {children}
    </section>
  );
}

function PlanManagementPanel() {
  const currentSubscription = useGetCurrentlySelectedPlan();
  const isSubscriptionCancelled = currentSubscription?.status === 'cancelled';
  const filteredActionGroups = filterActionGroups(
    isSubscriptionCancelled,
    actionGroups
  );

  return (
    <div className="mt-8 lg:mt-0">
      <h2 className="mb-2">Manage plans</h2>
      <PlainCard
        className="mb-6 rounded border-none p-4 shadow-none"
        as="article"
      >
        {filteredActionGroups.map((group: ActionGroup) => {
          return <ActionGroup key={group.title} group={group} />;
        })}
      </PlainCard>

      <PlanOverview />
    </div>
  );
}

function KeepNumberModal({ isOpen, onOpenChange }) {
  if (!isOpen) return null;

  return (
    <Modal onOpenChange={onOpenChange} open={isOpen}>
      <Modal.Overlay />
      <Modal.Content
        className="w-full rounded-lg p-6 lg:max-w-3xl"
        overflow="auto"
      >
        <div className="mb-4 flex justify-end">
          <Modal.Close>
            <CloseIcon />
          </Modal.Close>
        </div>
        <Modal.Title className="mb-4 text-xs font-semibold">
          Keep your existing number
        </Modal.Title>
        <Modal.Description as="div">
          <KeepYourNumber />
        </Modal.Description>
      </Modal.Content>
    </Modal>
  );
}

interface ModalConfig {
  buttonText: string;
  modalTitle: string;
  component: React.ComponentType<{ setIsOpen: (isOpen: boolean) => void }>;
  componentProps?: Record<string, object>;
  modalSize?: string;
  buttonClassName?: string;
  showTitle?: boolean;
}

export interface ActionGroup {
  title: string;
  actions: ModalConfig[];
}

function ModalButton({
  buttonText,
  modalTitle,
  component: Component,
  componentProps = {},
  modalSize = 'lg:max-w-3xl',
  buttonClassName = ' justify-self-start',
  showTitle = true
}: ModalConfig) {
  return (
    <FullDetailsButton text={buttonText} className={buttonClassName}>
      {({ isOpen, setIsOpen }) =>
        isOpen && (
          <Modal onOpenChange={setIsOpen} open={isOpen}>
            <Modal.Overlay />
            <Modal.Content
              className={`w-full rounded-lg p-6 ${modalSize}`}
              overflow="auto"
            >
              <div className="mb-4 flex justify-end">
                <Modal.Close>
                  <CloseIcon />
                </Modal.Close>
              </div>
              {showTitle && (
                <Modal.Title className="mb-4 text-xs font-semibold">
                  {modalTitle}
                </Modal.Title>
              )}
              <Modal.Description as="div">
                <Component {...componentProps} setIsOpen={setIsOpen} />
              </Modal.Description>
            </Modal.Content>
          </Modal>
        )
      }
    </FullDetailsButton>
  );
}

interface ActionGroupProps {
  group: ActionGroup;
}

function ActionGroup({ group }: ActionGroupProps) {
  return (
    <>
      <h3
        className={`${group.title === 'Subscriptions' ? '' : 'mt-4'}`}
      >
        {group.title}
      </h3>
      <Divider className="mt-1" />
      <div className="mt-2 grid grid-cols-1 place-items-start gap-y-4 lg:block lg:space-y-4 lg:space-x-4">
        {group.actions.map((action) => (
          <ModalButton
            key={action.buttonText}
            {...action}
            buttonClassName="deliverooLink"
          />
        ))}
      </div>
    </>
  );
}

function PlanOverview() {
  return (
    <>
      <h2 className="mt-8 mb-2">Plan overview</h2>
      <PlainCard
        as="article"
        className="mb-11 rounded border-none p-0 shadow-none"
      >
        <header className="bg-custom-gradient-header flex items-center justify-between rounded-tl rounded-tr p-4">
          <h3 className="text-secondary text-sm">Unlimited</h3>
          <strong className="text-secondary text-sm">£15</strong>
        </header>
        <div className="flex flex-wrap gap-4 px-4 py-6">
          {unlimitedPlanPerks.map((perk) => (
            <span
              key={perk}
              className="chip text-secondary border-black bg-[#6420CE]"
            >
              {perk}
            </span>
          ))}
        </div>
      </PlainCard>
    </>
  );
}

interface AdditionalPurchaseConfirmationModalProps {
  additionalPurchaseModalOpen: boolean;
  handleAdditionalPurchaseModalOpenChange: () => void;
}

function AdditionalPurchaseConfirmationModal({
  additionalPurchaseModalOpen,
  handleAdditionalPurchaseModalOpenChange
}: AdditionalPurchaseConfirmationModalProps) {
  return (
    <GenericModal
      isOpen={additionalPurchaseModalOpen}
      setIsOpen={handleAdditionalPurchaseModalOpenChange}
      modalTitle=""
    >
      <div className="flex flex-col items-center justify-center p-4">
        <div className="animate-scale-in bg-primary flex h-24 w-24 items-center justify-center rounded-full">
          <svg
            className="animate-check-draw h-12 w-12 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={3}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
      </div>
      <h3 className="animate-fade-in mb-4 text-center text-lg font-bold">
        Success!
      </h3>
      <p className="text-xxs text-center">
        Your order is confirmed! 🎉 You can now close this window, and your new
        eSIM will appear in your plans.
      </p>
    </GenericModal>
  );
}

const actionGroups: ActionGroup[] = [
  {
    title: 'Subscriptions',
    actions: [
      {
        buttonText: 'Cancel plan',
        modalTitle: "We're sorry to see you go",
        component: CancelPlan
      }
    ]
  },
  {
    title: 'Plan',
    actions: [
      {
        buttonText: 'Label your plan',
        modalTitle: 'Label your plan',
        component: LabelPlan,
        modalSize: 'lg:max-w-xl'
      },
      {
        buttonText: 'Keep your number',
        modalTitle: 'Keep your existing number',
        component: KeepYourNumber
      }
    ]
  },
  {
    title: 'eSIM',
    actions: [
      {
        buttonText: 'Installation instructions',
        modalTitle: 'eSIM Installation instructions',
        component: ESIMInstructions,
        buttonClassName: 'mt-2 w-fit',
        showTitle: false
      }
    ]
  }
];

const planDetailsCarouselStyles = {
  card: 'border-none subscription-card-shadow',
  header: 'bg-custom-gradient-header',
  planType: 'text-black',
  action: 'bg-black!',
  dataRemainingTime: 'text-black',
  lowDataWarning: 'bg-[#E4FFC1]!',
  status: '',
  addNewPlanButton:
    'hover:bg-teal-darker! hover:border-teal-darker! bg-primary! text-secondary! border-primary!',
  addNewPlanButtonIconFill: 'white'
};

interface AdditionalPurchaseGuardProps {
  children: React.ReactNode;
}

export function AdditionalPurchaseGuard({
  children
}: AdditionalPurchaseGuardProps) {
  const { isOpen, closeModal } = useUrlParamModal('purchase-confirmation');

  if (isOpen) {
    return (
      <AdditionalPurchaseConfirmationModal
        additionalPurchaseModalOpen={isOpen}
        handleAdditionalPurchaseModalOpenChange={closeModal}
      />
    );
  }

  return <>{children}</>;
}
