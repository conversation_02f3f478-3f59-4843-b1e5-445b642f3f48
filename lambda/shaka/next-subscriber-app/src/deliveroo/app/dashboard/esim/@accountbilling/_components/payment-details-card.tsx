import React, { useState, useEffect } from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import Image from 'next/image';
import stripeIcon from '@/public/images/stripe-icon.png';
import Button from '@/components/button/button';
import { GenericModal } from '@/components/generic-modal/generic-modal';
import { StripeProvider } from '@/lib/stripe/stripe-provider';
import { PaymentElement } from '@stripe/react-stripe-js';
import { usePaymentFormSubmission } from '@/hooks/usePaymentFormSubmission';
import { useMutation } from '@tanstack/react-query';
import { paymentService } from '@/services/paymentService';
import { useAuth } from '@/auth/hooks/use-auth';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { LoadingSpinner } from '@/icons/icons';
import { StripeCheckoutElementsOptions } from '@stripe/stripe-js';

const cardUpdateElementsOptions: StripeCheckoutElementsOptions = {
  appearance: {
    theme: 'flat',
    rules: {
      '.AccordionItem': {
        backgroundColor: 'transparent',
        borderColor: 'transparent',
        paddingLeft: '1px',
        paddingRight: '1px'
      },
      '.Label': {
        color: '#141414',
        fontWeight: '700',
        marginBottom: '8px'
      },
      '.Input': {
        marginBottom: '8px',
        borderRadius: '2px',
        color: '#141414',
        backgroundColor: '#fff',
        border: '1px solid #dddde0'
      },
      '.Input:focus': {
        boxShadow: 'none',
        outline: '1px solid  #141414'
      },
      '.Input--invalid': {
        boxShadow: 'none',
        color: '#ea0040'
      },
      '.Error': {
        color: '#ea0040',
        fontSize: '14px'
      }
    },
    variables: {
      colorPrimary: '#141414',
      iconCardErrorColor: '#141414',
      iconCardCvcErrorColor: '#141414'
    }
  }
};

const returnUrl = '???';

function useCreateSetupSession() {
  const { apiClient } = useAuth();

  const {
    mutate: createSetupSession,
    data: setupSession,
    isPending,
    error
  } = useMutation({
    mutationFn: () => paymentService.createSetupSession(apiClient, returnUrl)
  });

  return {
    createSetupSession,
    setupSession,
    isPending,
    error
  };
}

// Card update form component
function CardUpdateForm({ onSuccess }: { onSuccess: () => void }) {
  const { handleSubmit, isPending, errors } = usePaymentFormSubmission();

  // Override the success behavior to close modal instead of redirecting
  const handleCardUpdateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await handleSubmit(e);
      onSuccess(); // Close modal on success
    } catch {
      // Error handling is already done in usePaymentFormSubmission
    }
  };

  return (
    <form onSubmit={handleCardUpdateSubmit}>
      <div className="mb-4">
        <h3 className="mb-2 text-lg font-semibold">Update Payment Method</h3>
        <p className="mb-4 text-sm text-gray-600">
          Enter your new card details below. Your card will be saved securely
          for future payments.
        </p>
      </div>

      <PaymentElement />

      <Button
        isLoading={isPending}
        className="bg-primary hover:bg-primary-hover text-bold mt-4 inline-block w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold text-white"
      >
        {isPending ? 'Updating...' : 'Update Payment Method'}
      </Button>

      {errors.length > 0 && (
        <FormAlert
          className="mt-4"
          title="Update failed"
          variant="error"
          titleStyles=""
          singleMessageStyles=""
          messages={[`Error: ${errors[0]}`]}
        />
      )}
    </form>
  );
}

export function UserPaymentDetailsCard() {
  const [isOpen, setIsOpen] = useState(false);
  const { createSetupSession, setupSession, isPending, error } =
    useCreateSetupSession();

  useEffect(() => {
    if (isOpen && !setupSession && !isPending) {
      createSetupSession();
    }
  }, [isOpen, setupSession, isPending, createSetupSession]);

  const handleUpdateSuccess = () => {
    setIsOpen(false);
    // Optionally refresh subscriber data or show success message
  };

  return (
    <PlainCard as="article" className="mb-12 rounded border-none shadow-none">
      <div className="bg-gray-subtle text-default mb-4 rounded p-3">
        <p >Card ending **** **** **** 2199</p>
      </div>
      <div className="text-default flex flex-wrap items-center justify-between gap-2">
        <div className="flex grow items-center gap-3">
          <p >Powered by</p>
          <Image width={74} height={31} src={stripeIcon} alt="Stripe icon" />
        </div>
        <Button onClick={() => setIsOpen(true)} variant="secondary">
          Edit Payment
        </Button>
        <GenericModal
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          modalTitle="Update Payment Method"
          modalSize="lg:max-w-md"
        >
          {error ? (
            <FormAlert
              className="mt-4"
              title="Setup Error"
              variant="error"
              messages={[`Error: ${error.message}`]}
            />
          ) : isPending ? (
            <div className="flex flex-col items-center justify-center py-8">
              <LoadingSpinner />
              <p className="mt-4 text-sm text-gray-600">
                Setting up payment form...
              </p>
            </div>
          ) : setupSession?.clientSecret ? (
            <StripeProvider
              clientSecret={setupSession.clientSecret}
              mode="custom"
              options={{
                appearance: cardUpdateElementsOptions.appearance
              }}
            >
              <CardUpdateForm onSuccess={handleUpdateSuccess} />
            </StripeProvider>
          ) : (
            <div className="py-8 text-center">
              <p className="text-sm text-gray-600">
                Unable to load payment form
              </p>
            </div>
          )}
        </GenericModal>
      </div>
    </PlainCard>
  );
}
