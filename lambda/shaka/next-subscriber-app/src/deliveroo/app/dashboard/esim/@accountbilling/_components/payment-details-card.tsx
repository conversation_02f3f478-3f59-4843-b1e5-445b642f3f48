import React, { useState } from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import Image from 'next/image';
import stripeIcon from '@/public/images/stripe-icon.png';
import Button from '@/components/button/button';
import { GenericModal } from '@/components/generic-modal/generic-modal';
import { PaymentElement, useCheckout } from '@stripe/react-stripe-js';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { ConfirmError } from '@stripe/stripe-js';
// TODO: Import StripeProvider when you have the setup session endpoint
// import { StripeProvider } from '@/lib/stripe/stripe-provider';

// Simple card update form component - matches SIMP app pattern
// This will be used when you have the setup session endpoint
function StripeChangeCardDetailsForm({ onSuccess }: { onSuccess: () => void }) {
  const { confirm } = useCheckout();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ConfirmError | null>(null);

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);
    setError(null);

    confirm().then((result) => {
      if (result.type === "error") {
        setError(result.error);
      } else {
        // Success - close modal
        onSuccess();
      }
      setLoading(false);
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="mb-4">
        <h3 className="mb-2 text-lg font-semibold">Update Payment Method</h3>
        <p className="mb-4 text-sm text-gray-600">
          Enter your new card details below. Your card will be saved securely
          for future payments.
        </p>
      </div>

      <PaymentElement />

      <div className="mt-6">
        {error && (
          <FormAlert
            className="mb-4"
            title="Update failed"
            variant="error"
            messages={[`Error: ${error.message}`]}
          />
        )}
      </div>

      <Button
        isLoading={loading}
        disabled={loading}
        className="bg-primary hover:bg-primary-hover text-bold mt-4 inline-block w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold text-white"
      >
        {loading ? 'Processing...' : 'Update Payment Method'}
      </Button>
    </form>
  );
}

export function UserPaymentDetailsCard() {
  const [isOpen, setIsOpen] = useState(false);

  const handleUpdateSuccess = () => {
    setIsOpen(false);
    // Optionally refresh subscriber data or show success message
  };

  const handleModalClose = () => {
    setIsOpen(false);
  };

  return (
    <PlainCard as="article" className="mb-12 rounded border-none shadow-none">
      <div className="bg-gray-subtle text-default mb-4 rounded p-3">
        <p>Card ending **** **** **** 2199</p>
      </div>
      <div className="text-default flex flex-wrap items-center justify-between gap-2">
        <div className="flex grow items-center gap-3">
          <p>Powered by</p>
          <Image width={74} height={31} src={stripeIcon} alt="Stripe icon" />
        </div>

        <Button onClick={() => setIsOpen(true)} variant="secondary">
          Edit Payment
        </Button>
        <GenericModal
          isOpen={isOpen}
          setIsOpen={handleModalClose}
          modalTitle="Update Payment Method"
          modalSize="lg:max-w-md"
        >
          <div className="py-8 text-center">
            <FormAlert
              className="mb-4"
              title="Setup Required"
              variant="error"
              messages={[
                'Payment form setup requires a backend endpoint to create a Stripe setup session.',
                'Please implement the setup session API endpoint first.'
              ]}
            />
            <p className="text-sm text-gray-600">
              Once you have the setup session endpoint, you can use the StripeProvider
              with the clientSecret to enable card updates.
            </p>
          </div>
          {/*
          TODO: Uncomment this when you have the setup session endpoint:

          <StripeProvider
            clientSecret="your-client-secret-from-backend"
            mode="setup"
          >
            <StripeChangeCardDetailsForm onSuccess={handleUpdateSuccess} />
          </StripeProvider>
          */}
        </GenericModal>
      </div>
    </PlainCard>
  );
}
