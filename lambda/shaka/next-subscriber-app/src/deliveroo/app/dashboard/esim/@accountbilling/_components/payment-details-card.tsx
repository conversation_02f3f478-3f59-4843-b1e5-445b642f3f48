import React, { useState } from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import Image from 'next/image';
import stripeIcon from '@/public/images/stripe-icon.png';
import Button from '@/components/button/button';
import { GenericModal } from '@/components/generic-modal/generic-modal';
import { PaymentElement, useCheckout } from '@stripe/react-stripe-js';
import { StripeProvider } from '@/lib/stripe/stripe-provider';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { ConfirmError, StripeCheckoutElementsOptions } from '@stripe/stripe-js';

const elementsOptions: StripeCheckoutElementsOptions = {
  appearance: {
    theme: 'flat',
    rules: {
      '.AccordionItem': {
        backgroundColor: 'transparent',
        borderColor: 'transparent',
        paddingLeft: '1px',
        paddingRight: '1px'
      },
      '.Label': {
        color: '#141414',
        fontWeight: '700',
        marginBottom: '8px'
      },
      '.Input': {
        marginBottom: '8px',
        borderRadius: '2px',
        color: '#141414',
        backgroundColor: '#fff',
        border: '1px solid #dddde0'
      },
      '.Input:focus': {
        boxShadow: 'none',
        outline: '1px solid  #141414'
      },
      '.Input--invalid': {
        boxShadow: 'none',
        color: '#ea0040'
      },
      '.Error': {
        color: '#ea0040',
        fontSize: '14px'
      }
    },
    variables: {
      colorPrimary: '#141414',
      iconCardErrorColor: '#141414',
      iconCardCvcErrorColor: '#141414'
    }
  }
};

function StripeChangeCardDetailsForm() {
  const { confirm } = useCheckout();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ConfirmError | null>(null);

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);
    setError(null);

    confirm().then((result) => {
      if (result.type === "error") {
        setError(result.error);
      }
      setLoading(false);
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <PaymentElement />
      <div className="mt-6">
        {error && (
          <div className="mb-4">
            <FormAlert
              title="Update failed"
              variant="error"
              messages={[error.message || 'An error occurred']}
            />
          </div>
        )}
      </div>
      <Button
        className="mb-6"
        disabled={loading}
        isLoading={loading}
        variant="filled"
      >
        {loading ? "Processing..." : "Change card details"}
      </Button>
    </form>
  );
}

export function UserPaymentDetailsCard() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <PlainCard as="article" className="mb-12 rounded border-none shadow-none">
      <div className="bg-gray-subtle text-default mb-4 rounded p-3">
        <p>Card ending **** **** **** 2199</p>
      </div>
      <div className="text-default flex flex-wrap items-center justify-between gap-2">
        <div className="flex grow items-center gap-3">
          <p>Powered by</p>
          <Image width={74} height={31} src={stripeIcon} alt="Stripe icon" />
        </div>

        <Button onClick={() => setIsOpen(true)} variant="secondary">
          Edit Payment
        </Button>
        <GenericModal
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          modalTitle="Update Payment Method"
          modalSize="lg:max-w-md"
        >
          <div className="py-8 text-center">
            <p className="text-sm text-gray-600">
              Payment form will be implemented here.
            </p>
          </div>
        </GenericModal>
      </div>
    </PlainCard>
  );
}
