import React, { useState } from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import Image from 'next/image';
import stripeIcon from '@/public/images/stripe-icon.png';
import Button from '@/components/button/button';
import { GenericModal } from '@/components/generic-modal/generic-modal';

export function UserPaymentDetailsCard() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <PlainCard as="article" className="mb-12 rounded border-none shadow-none">
      <div className="bg-gray-subtle text-default mb-4 rounded p-3">
        <p>Card ending **** **** **** 2199</p>
      </div>
      <div className="text-default flex flex-wrap items-center justify-between gap-2">
        <div className="flex grow items-center gap-3">
          <p>Powered by</p>
          <Image width={74} height={31} src={stripeIcon} alt="Stripe icon" />
        </div>

        <Button onClick={() => setIsOpen(true)} variant="secondary">
          Edit Payment
        </Button>
        <GenericModal
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          modalTitle="Update Payment Method"
          modalSize="lg:max-w-md"
        >
          <div className="py-8 text-center">
            <p className="text-sm text-gray-600">
              Payment form will be implemented here.
            </p>
          </div>
        </GenericModal>
      </div>
    </PlainCard>
  );
}
