import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { UserPaymentDetailsCard } from '../payment-details-card';
import { useAuth } from '@/auth/hooks/use-auth';
import { paymentService } from '@/services/paymentService';

// Mock dependencies
jest.mock('@/auth/hooks/use-auth');
jest.mock('@/services/paymentService');
jest.mock('@/lib/stripe/stripe-provider', () => ({
  StripeProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="stripe-provider">{children}</div>
}));

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockPaymentService = paymentService as jest.Mocked<typeof paymentService>;

describe('UserPaymentDetailsCard', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    });

    mockUseAuth.mockReturnValue({
      apiClient: {} as any,
      isAuthenticated: true,
      isLoading: false,
      error: null,
      login: jest.fn(),
      signup: jest.fn(),
      logout: jest.fn(),
      refreshToken: jest.fn()
    });

    // Mock successful API response
    mockPaymentService.createSetupSession.mockResolvedValue({
      clientSecret: 'test-client-secret',
      sessionId: 'test-session-id'
    });

    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        origin: 'https://test.com'
      },
      writable: true
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <UserPaymentDetailsCard />
      </QueryClientProvider>
    );
  };

  it('should not call API repeatedly when modal is opened', async () => {
    renderComponent();

    // Open modal
    const editButton = screen.getByText('Edit Payment');
    fireEvent.click(editButton);

    // Wait for API call
    await waitFor(() => {
      expect(mockPaymentService.createSetupSession).toHaveBeenCalledTimes(1);
    });

    // Wait a bit more to ensure no additional calls
    await new Promise(resolve => setTimeout(resolve, 100));
    
    expect(mockPaymentService.createSetupSession).toHaveBeenCalledTimes(1);
  });

  it('should reset state when modal is closed', async () => {
    renderComponent();

    // Open modal
    const editButton = screen.getByText('Edit Payment');
    fireEvent.click(editButton);

    await waitFor(() => {
      expect(mockPaymentService.createSetupSession).toHaveBeenCalledTimes(1);
    });

    // Close modal (simulate clicking close button)
    // Note: This would need to be adjusted based on actual modal implementation
    // For now, we're testing the concept
  });
});
