import { PlainCard } from '@/components/plain-card/plain-card';
import { Divider } from '@/components/divider/divider';
import { FullDetailsButton } from '@/components/full-details-button/full-details-button';
import React from 'react';
import { GenericModal } from '@/components/generic-modal/generic-modal';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';

interface Bill {
  id: string;
  month: string;
  amount: string;
  paymentStatus: string;
}

const userBills: Bill[] = [
  {
    id: '1',
    month: 'February 2025',
    amount: '34',
    paymentStatus: 'paid'
  },
  {
    id: '2',
    month: 'March 2025',
    amount: '34',
    paymentStatus: 'pending'
  },
  {
    id: '3',
    month: 'April 2025',
    amount: '34',
    paymentStatus: 'failed'
  }
];

const paymentStatusConfig: Record<string, string> = {
  paid: 'bg-success-border',
  pending: 'bg-warning-border',
  failed: 'bg-red-500'
};

export function UserPaidBillsList() {
  // api call via hook here
  const noPaidBills = userBills.length === 0;
  return (
    <PlainCard className="rounded border-none p-4 shadow-none">
      <h3>Paid bills</h3>
      <Divider className="mt-2" />
      {noPaidBills ? (
        <FormAlert
          variant="info"
          messages="You have no paid bills"
          dismissible={false}
        />
      ) : (
        userBills.map((bill) => (
          <UserPaidBillsListItem
            key={bill.id}
            bill={bill}
            // onDownload={() => {
            //   console.log('download');
            // }}
          />
        ))
      )}
    </PlainCard>
  );
}

interface UserPaidBillsListItemProps {
  bill: Bill;
  // onDownload: (id: string) => void;
}

function UserPaidBillsListItem({
  bill
  // onDownload
}: UserPaidBillsListItemProps) {
  const { month, amount, paymentStatus } = bill;

  return (
    <div className="border-border mb-5 grid grid-cols-3 grid-rows-[1fr_auto] place-items-start justify-items-end gap-y-0 border-b lg:grid-cols-[110px_2fr_auto_auto] lg:grid-rows-1 lg:gap-x-4">
      <div className="justify-self-start lg:mb-3">
        <p className="text-xxxs">{month}</p>
        <strong className="block text-xs">£{amount}</strong>
      </div>
      <div className="col-start-3 row-start-1 flex items-center gap-2 lg:col-start-2 lg:justify-self-start">
        <div
          className={`${paymentStatusConfig[paymentStatus]} h-2 w-2 rounded-full`}
        ></div>
        <p className="text-xxxs inline">{paymentStatus}</p>
      </div>
      <FullDetailsButton
        className="col-start-2 row-start-2 lg:col-start-3 lg:row-start-1"
        text="View details"
      >
        {({ isOpen, setIsOpen }) =>
          isOpen && (
            <GenericModal
              isOpen={isOpen}
              setIsOpen={setIsOpen}
              modalTitle="View details"
              modalDescription={`for ${month}`}
            />
          )
        }
      </FullDetailsButton>
      {/*<button*/}
      {/*  onClick={() => onDownload(id)}*/}
      {/*  className="uswitchLink col-start-3 row-start-2 mb-2 lg:col-start-4 lg:row-start-1"*/}
      {/*>*/}
      {/*  Download*/}
      {/*</button>*/}
    </div>
  );
}
