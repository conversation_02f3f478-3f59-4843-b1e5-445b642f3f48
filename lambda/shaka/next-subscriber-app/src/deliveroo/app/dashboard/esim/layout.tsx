'use client';

import { useSearchPara<PERSON>, useRouter } from 'next/navigation';
import React, { Suspense, useState, useTransition } from 'react';
import { YourPlansLoadingSkeleton } from '@/src/deliveroo/app/dashboard/esim/@yourplans/_components/your-plans-skeleton';
import { AccountBillingSkeleton } from '@/src/deliveroo/app/dashboard/esim/@yourplans/_components/account-billing-skeleton';
import { HelpSectionSkeleton } from '@/src/deliveroo/app/dashboard/esim/@yourplans/_components/help-section-skeleton';
import { tabConfig } from '@/src/deliveroo/utils/constants';
import useLocalStorage from '@/hooks/useLocalStorage';

const TAB_STORAGE_KEY = 'esim-active-tab';

function getTabButtonClass(tab: string, activeTab: string) {
  return `after:bg-black relative cursor-pointer px-3 py-2 transition-colors duration-200 after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:transition-all after:duration-300 after:ease-out ${
    tab === activeTab
      ? 'font-bold after:scale-x-100'
      : 'hover:text-primary after:scale-x-0 opacity-70'
  }`;
}

export type Tab = (typeof tabConfig)[number]['value'];

const tabFallbacks = {
  'your-plans': <YourPlansLoadingSkeleton withTabs />,
  'account-billing': <AccountBillingSkeleton withTabs />,
  help: <HelpSectionSkeleton withTabs />
};

interface EsimLayoutProps {
  yourplans: React.ReactNode;
  accountbilling: React.ReactNode;
  help: React.ReactNode;
}

function EsimLayoutContent({
  yourplans,
  accountbilling,
  help
}: EsimLayoutProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [savedTab, setSavedTab] = useLocalStorage(TAB_STORAGE_KEY, '');
  const currentTab = (searchParams.get('tab') || savedTab) as Tab;
  const [activeTab, setActiveTab] = useState<Tab>(
    currentTab || tabConfig[0].value
  );

  function handleTabClick(value: Tab) {
    setActiveTab(value);
    startTransition(() => {
      router.push(`/dashboard/esim?tab=${value}`);
      setSavedTab(value);
    });
  }

  const tabContent = {
    'your-plans': yourplans,
    'account-billing': accountbilling,
    help
  };

  return (
    <div>
      <h1 className="mb-6">Deliveroo eSIM</h1>
      <nav title="tab navigation" className="relative mb-4">
        <div className="relative flex border-b border-gray-300">
          {tabConfig.map(({ value, label }) => (
            <button
              key={value}
              onClick={() => handleTabClick(value)}
              className={getTabButtonClass(value, activeTab)}
              disabled={isPending}
            >
              {label}
            </button>
          ))}
        </div>
      </nav>

      {tabConfig.map(({ value: tab }) => (
        <div key={tab} className={activeTab === tab ? 'block' : 'hidden'}>
          <Suspense fallback={tabFallbacks[tab]}>{tabContent[tab]}</Suspense>
        </div>
      ))}
    </div>
  );
}

function EsimLayoutFallback() {
  return (
    <div>
      <h1 className="mb-6">eSIM</h1>
      <nav className="relative mb-4">
        <div className="relative flex border-b border-gray-300">
          {tabConfig.map(({ value, label }) => (
            <div
              key={value}
              className="after:bg-primary text-primary relative px-3 py-2 transition-colors duration-200 after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:scale-x-0 after:transition-all after:duration-300 after:ease-out"
            >
              {label}
            </div>
          ))}
        </div>
      </nav>
      <YourPlansLoadingSkeleton />
    </div>
  );
}

export default function EsimLayout(props: EsimLayoutProps) {
  return (
    <Suspense fallback={<EsimLayoutFallback />}>
      <EsimLayoutContent {...props} />
    </Suspense>
  );
}
