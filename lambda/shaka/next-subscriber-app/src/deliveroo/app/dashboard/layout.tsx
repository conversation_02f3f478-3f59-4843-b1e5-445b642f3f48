'use client';

import React, { useTransition } from 'react';
import { RequireAuth } from '@/components/require-auth/require-auth';
import { NavBar } from '@/components/nav-bar/nav-bar';
import Image from 'next/image';
import logo from '@/src/deliveroo/public/images/deliveroo-logo.png';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import Button from '@/components/button/button';
import { useAuth } from '@/auth/hooks/use-auth';
import { useCurrentSignupStep } from '@/hooks/useCurrentSignupStep';
import { Loader } from '@/components/loader/loader';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { logout } = useAuth();
  const [isPending, startTransition] = useTransition();
  const { clearCurrentStep } = useCurrentSignupStep({
    requiresAuth: true,
    brand: 'deliveroo'
  });

  const handleLogout = async () => {
    startTransition(async () => {
      clearCurrentStep();
      localStorage.clear();
      await logout();
    });
  };

  return (
    <RequireAuth
      redirectTo={ROUTES_CONFIG['login'].path}
      loadingComponent={<Loader />}
    >
      {isPending ? (
        <Loader textStyles="" text="Logging out..." />
      ) : (
        <div className="bg-gray-subtle">
          <NavBar
            logo={
              <Image
                src={logo}
                priority
                alt="Deliveroo logo"
                width={236}
                height={30}
              />
            }
            className="min-h-[56px]"
            logoutButton={
              <Button
                isLoading={isPending}
                disabled={isPending}
                onClick={handleLogout}
                className="bg-black!"
              >
                Logout
              </Button>
            }
          />
          <section
            style={{ maxWidth: 'clamp(375px, 70vw, 1920px)' }}
            className="layout-height mx-auto flex gap-8 p-4 lg:px-2 lg:py-8 xl:px-8 2xl:px-12"
          >
            <main className="mx-auto flex-1">{children}</main>
          </section>
        </div>
      )}
    </RequireAuth>
  );
}
