import React from 'react';
import { faq } from '@/src/deliveroo/app/_landing-page-constants/constants';
import { FaqCard } from '@/src/deliveroo/app/_landing-page-components/faq/faq';

export default function CommonQuestions() {
  return (
    <section id="faq" className="scroll-mt-20">
      <h4 className="my-12 text-center text-[40px] leading-[55px] font-medium uppercase">
        Common questions
      </h4>
      <div className="space-y-4 lg:flex lg:flex-wrap lg:items-stretch lg:gap-4 lg:space-y-0">
        {faq.map((item) => {
          return (
            <div key={item.id} className="lg:flex lg:w-[calc(50%-0.5rem)]">
              <FaqCard title={item.title}>
                <p className="px-6 text-pretty">{item.content}</p>
              </FaqCard>
            </div>
          );
        })}
      </div>
    </section>
  );
}
