import React, { useState } from 'react';
import Button from '@/components/button/button';
import { GenericModal } from '@/components/generic-modal/generic-modal';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { SignupForm } from '../signup-form/signup-form';
import { useDeliverooRiderAuthSubmission } from '@/hooks/useDeliverooRiderAuthSubmission';
import { useFocusError } from '@/hooks/useFocusError';
import { getFieldError } from '@/utils/helpers';

interface SignupButtonProps {
  isLoginSubmissionPending: boolean;
  openInitially?: boolean;
}

export function SignupButton({
  isLoginSubmissionPending,
  openInitially = false
}: SignupButtonProps) {
  const [openSignupModal, setOpenSignupModal] = useState(openInitially);
  const { handleFormSubmit, isPending, errors, formData, setErrors } =
    useDeliverooRiderAuthSubmission();
  useFocusError(errors);

  const errorMap = {
    rider_id: getFieldError('rider_id', errors),
    email: getFieldError('email', errors)
  } as const;

  const clearErrors = () => setErrors([]);
  const isApiError = errors.length > 0 && !errorMap.rider_id && !errorMap.email;

  return (
    <>
      <Button
        disabled={isLoginSubmissionPending}
        variant="primary"
        type="button"
        className="mt-4 w-full border-none bg-black! p-3 hover:bg-black/60! disabled:w-full disabled:text-white disabled:opacity-50"
        onClick={() => setOpenSignupModal(true)}
      >
        Sign up
      </Button>
      {openSignupModal && (
        <GenericModal
          disableCloseButton={isPending}
          isOpen={openSignupModal}
          setIsOpen={(value) => {
            setOpenSignupModal(value);
            clearErrors();
          }}
          modalTitle="Rider authentication"
          titleStyles="text-center text-xs"
        >
          <div className="lg:px-12">
            {isApiError ? (
              <FormAlert
                dismissible={false}
                className="text-pretty"
                variant="deliverooInfo"
                messages={[
                  'message: If you are a new rider, it can take up to a day from your first completed delivery for your account to become eligible.'
                ]}
              />
            ) : (
              <FormAlert
                dismissible={false}
                className="text-pretty"
                variant="deliveroo"
                messages={[
                  'message: Rider eSIM is only available to eligible UK Deliveroo riders. Use your Rider ID and Deliveroo-registered email to authenticate your account.'
                ]}
              />
            )}
            <SignupForm
              handleFormSubmit={handleFormSubmit}
              formData={formData}
              isSignupSubmissionPending={isPending}
              clearErrors={clearErrors}
              errorMap={errorMap}
              isApiError={isApiError}
              errors={errors}
            />
          </div>
        </GenericModal>
      )}
    </>
  );
}
