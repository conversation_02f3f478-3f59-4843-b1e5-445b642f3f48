// move to separate cmponent
import React, { useState } from 'react';
import Button from '@/components/button/button';
import { GenericModal } from '@/components/generic-modal/generic-modal';
import { Alert } from '@/components/alert/alert';
import { getFieldError } from '@/utils/helpers';
import { useForgotPasswordFormSubmission } from '@/hooks/useForgotPasswordFormSubmission';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';

export function ForgotPasswordButton() {
  const {
    errors,
    setErrors,
    formData,
    isPending,
    handleSubmit,
    successMessage,
    setSuccessMessage
  } = useForgotPasswordFormSubmission();
  const [openForgotPasswordModal, setOpenForgotPasswordModal] = useState(false);

  const clearErrors = () => setErrors([]);
  const errorMap = {
    forgotPasswordEmail: getFieldError('email', errors) || ''
  };

  const hasError = errorMap.forgotPasswordEmail || errors.length > 0;

  const handleModalClose = () => {
    setOpenForgotPasswordModal(false);
    setSuccessMessage(false);
  };

  return (
    <>
      <Button
        type="button"
        className="deliverooLink text-primary! bg-transparent font-normal hover:bg-transparent"
        onClick={() => setOpenForgotPasswordModal(true)}
      >
        Forgot password?
      </Button>
      {openForgotPasswordModal && (
        <GenericModal
          disableCloseButton={isPending}
          isOpen={openForgotPasswordModal}
          setIsOpen={handleModalClose}
          modalTitle="Forgot password ?"
          titleStyles="text-center text-xs"
        >
          <div className="lg:px-12">
            <p className="mb-4 text-center">
              Enter your email to get a reset link.
            </p>
            <form onSubmit={handleSubmit}>
              <label className="sr-only" htmlFor="forgotPasswordEmail">
                Email
              </label>
              <input
                autoComplete="email"
                type="text"
                name="email"
                id="forgotPasswordEmail"
                placeholder="Email address used to sign up"
                defaultValue={formData.email}
                onChange={clearErrors}
                className={`w-full rounded-sm border-0 bg-[#EFEEEE] px-4 py-3 placeholder-[#C5C5C5] focus:ring-2 focus:ring-teal-400 focus:outline-none ${errorMap.forgotPasswordEmail ? 'border-2 border-red-500' : ''}`}
              />
              {hasError && (
                <Alert
                  variant="error"
                  message={errorMap.forgotPasswordEmail || errors[0]}
                  className="mx-auto mt-4 w-fit"
                />
              )}
              <Button
                disabled={isPending}
                className="mt-4 w-full"
                variant="primary"
              >
                Send link
              </Button>
              {successMessage && (
                <FormAlert
                  className="mt-4 lg:mt-6"
                  title="Success"
                  variant="deliveroo"
                  titleStyles=""
                  singleMessageStyles=""
                  messages={['success: Check your email for a reset link.']}
                />
              )}
            </form>
          </div>
        </GenericModal>
      )}
    </>
  );
}
