'use client';

import '../globals.css';
import deliverooRider from '../../public/images/deliveroo-rider.png';
import ridersLogo from '../../public/images/Rider-logo-login-screen.png';
import Image from 'next/image';
import Button from '@/components/button/button';
import React, { useEffect, useTransition } from 'react';
import { useLoginFormSubmission } from '@/hooks/useLoginFormSubmission';
import { Alert } from '@/components/alert/alert';
import { getFieldError } from '@/utils/helpers';
import { useFocusError } from '@/hooks/useFocusError';
import { useRouter, useSearchParams } from 'next/navigation';
import { SignupButton } from '@/src/deliveroo/app/login/_components/signup-button/signup-button';
import { ForgotPasswordButton } from '@/src/deliveroo/app/login/_components/forgot-password/forgot-password';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { Shaka<PERSON>ogoBlack, ShakaLogoWhite } from '@/icons/icons';
import { useIsAuthenticated } from '@/auth/hooks/use-auth';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { Loader } from '@/components/loader/loader';
// import { SignupStepRestorer } from '@/components/signup-step-restorer/signup-step-restorer';

function LoginPhoto() {
  return (
    <aside className="p-4 md:pl-4">
      <Image
        src={deliverooRider}
        priority
        alt="Deliveroo rider illustration"
        width={436}
        height={600}
        className="w-full"
      />
    </aside>
  );
}

function LoginHeader() {
  return (
    <header className="mb-8 text-center">
      <div className="mb-2 flex items-center justify-center">
        <Image
          src={ridersLogo}
          priority
          alt="Deliveroo riders logo"
          width={158}
          height={31}
          quality={100}
        />
      </div>
      <div className="mt-4 flex items-center justify-center">
        <DeliverooLogo />
      </div>
    </header>
  );
}

function LoginBody() {
  const { isAuthenticated } = useIsAuthenticated();
  const router = useRouter();
  const searchParams = useSearchParams();
  const hasSignupParam = searchParams.has('signup');
  const [isPending, startTransition] = useTransition();

  useEffect(() => {
    if (isAuthenticated) {
      startTransition(() => {
        router.push(ROUTES_CONFIG.dashboard.path);
      });
    }
  }, [isAuthenticated, router]);

  const {
    handleFormSubmit,
    submitLoginForm,
    errors,
    mutationError,
    formData,
    setErrors
  } = useLoginFormSubmission();

  useFocusError(errors);

  const errorMap = {
    email: getFieldError('email', errors),
    password: getFieldError('password', errors)
  } as const;

  const clearErrors = () => setErrors([]);

  return (
    <>
      {isPending ? (
        <Loader className="h-92!" textStyles="" text="Logging in..." />
      ) : (
        <form id="login-form" onSubmit={handleFormSubmit} className="space-y-4">
          <label className="block" htmlFor="email">
            <input
              id="email"
              name="email"
              type="text"
              inputMode="email"
              placeholder="Email"
              autoComplete="email"
              defaultValue={formData.email}
              onChange={clearErrors}
              className={`w-full rounded-sm border-0 bg-[#EFEEEE] px-4 py-3 placeholder-[#8E8E8E] focus:ring-2 focus:ring-teal-400 focus:outline-none ${errorMap.email ? 'border-2 border-red-500' : ''}`}
            />
          </label>
          {errorMap.email && (
            <Alert
              variant="error"
              message={errorMap.email}
              align="left"
              className="mb-4"
            />
          )}
          <label className="block" htmlFor="password">
            <input
              id="password"
              name="password"
              type="password"
              placeholder="Password"
              autoComplete="password"
              defaultValue={formData.password}
              onChange={clearErrors}
              className={`w-full rounded-sm border-0 bg-[#EFEEEE] px-4 py-3 placeholder-[#8E8E8E] focus:ring-2 focus:ring-teal-400 focus:outline-none ${errorMap.password ? 'border-2 border-red-500' : ''}`}
            />
          </label>
          {errorMap.password && (
            <Alert
              variant="error"
              message={errorMap.password}
              align="left"
              className="mb-4"
            />
          )}

          {mutationError && (
            <Alert
              variant="error"
              message={errors[0]}
              className="mx-auto mt-4 w-fit"
            />
          )}
          <Button
            isLoading={submitLoginForm}
            disabled={submitLoginForm}
            className="bg-primary text-secondary border-primary hover:bg-primary-hover hover:text-secondary mb-0 w-full"
            form="login-form"
          >
            Login
          </Button>
          <div className="text-center">
            <ForgotPasswordButton />
          </div>
          <div className="my-4 h-px w-full bg-gray-200" />
          <SignupButton
            isLoginSubmissionPending={submitLoginForm}
            openInitially={hasSignupParam}
          />
        </form>
      )}
    </>
  );
}

function LoginFooterDesktop() {
  return (
    <footer className="mt-8 text-center">
      <p className="text-xxxs mb-2 text-[#A3A3A3]">
        Rider eSIM is operated by shaka in partnership with Deliveroo
      </p>
      <ShakaLogoBlack className="mx-auto" />
    </footer>
  );
}

function LoginFooterMobile() {
  return (
    <footer className="mt-8 text-center">
      <p className="text-xxxs font-regular mb-4 max-w-[300px] text-white">
        Rider eSIM is operated by shaka in partnership with Deliveroo
      </p>
      <ShakaLogoWhite className="mx-auto" />
    </footer>
  );
}

export default function DeliverooRiderLogin() {
  const isMobile = useMediaQuery(1024);

  if (isMobile) {
    return <LoginMobile />;
  }

  return <LoginDesktop />;
}

function LoginDesktop() {
  return (
    <main className="bg-gray-subtle grid min-h-screen place-items-center p-4 md:px-4 md:py-16">
      {/*tests fails */}
      {/*<SignupStepRestorer />*/}
      <div className="mx-auto grid w-full max-w-5xl grid-cols-2 overflow-hidden rounded-lg bg-white">
        <LoginPhoto />
        <section className="flex w-full flex-col items-center justify-center">
          <div className="mt-6 w-full max-w-sm p-4 md:mt-0 md:p-0">
            <LoginHeader />
            <LoginBody />
          </div>
        </section>
      </div>
      <LoginFooterDesktop />
    </main>
  );
}

function LoginMobile() {
  return (
    <main className="bg-login-image-mobile grid min-h-screen place-items-center p-4 md:px-4 md:py-16">
      <div className="fade-in mx-auto mt-4 w-full max-w-5xl overflow-hidden rounded-lg bg-white">
        <div className="mobile-backdrop-blur w-full px-4 py-12">
          <LoginHeader />
          <LoginBody />
        </div>
      </div>
      <LoginFooterMobile />
    </main>
  );
}

function DeliverooLogo() {
  return (
    <svg
      width="145"
      height="39"
      viewBox="0 0 300 80"
      xmlns="http://www.w3.org/2000/svg"
    >
      <title>Artboard</title>
      <path
        d="M116.41 59.926l-1.133-5.28V23.172h-6.542v14.357c-1.968-2.27-4.675-3.6-7.87-3.6-6.545 0-11.61 5.376-11.61 13.27 0 7.895 5.065 13.272 11.61 13.272 3.246 0 6.05-1.383 8.018-3.752l.688 3.207h6.84zm126.18-17.81l3.1-6.956c-1.428-.89-3.1-1.283-4.722-1.283-2.952 0-5.412 1.328-6.985 3.8l-.69-3.21h-6.74l1.082 4.982V59.92h6.54v-16.23c.885-1.677 2.56-2.712 4.723-2.712 1.332.002 2.56.346 3.692 1.135zm8.904 5.08c0-3.946 2.806-6.41 6.59-6.41 3.84 0 6.595 2.464 6.595 6.41 0 3.9-2.756 6.415-6.594 6.415-3.786 0-6.592-2.514-6.592-6.413zm-155.895 0c0-3.946 2.803-6.41 6.59-6.41 3.84 0 6.593 2.464 6.593 6.41 0 3.9-2.754 6.415-6.59 6.415-3.788 0-6.594-2.514-6.594-6.413zm191.462 6.415c-3.787 0-6.59-2.514-6.59-6.413 0-3.947 2.803-6.412 6.59-6.412 3.837 0 6.59 2.465 6.59 6.412 0 3.9-2.753 6.414-6.59 6.414zM138.594 44.88h-12.592c.738-2.91 2.95-4.49 6.295-4.49 3.395 0 5.61 1.58 6.297 4.49zm78.268 0H204.27c.737-2.91 2.95-4.49 6.295-4.49 3.395 0 5.61 1.58 6.297 4.49zm-27.695 15.048l6.84-25.457h-7.134l-5.016 20.622-5.02-20.623h-7.035l6.84 25.455h10.525zm-27.4 0h6.538V34.468h-6.54v25.458zm-18.233-2.44l-2.508-5.623c-2.362 1.233-4.97 1.973-7.626 1.973-3.442 0-5.853-1.332-6.887-3.8h18.397c.197-.936.297-1.873.297-3.008 0-7.894-5.46-13.173-12.79-13.173-7.378 0-12.79 5.33-12.79 13.272 0 8.09 5.362 13.27 13.676 13.27 3.64 0 7.23-.987 10.23-2.91zm143.528-23.56c-7.43 0-12.938 5.377-12.938 13.27 0 7.896 5.51 13.272 12.937 13.272 7.43 0 12.94-5.376 12.94-13.27 0-7.895-5.51-13.272-12.938-13.272zm-65.377 23.63l-2.51-5.625c-2.363 1.234-4.97 1.973-7.627 1.973-3.443 0-5.854-1.33-6.887-3.8h18.4c.197-.936.294-1.873.294-3.007 0-7.894-5.46-13.173-12.79-13.173-7.38 0-12.79 5.327-12.79 13.272 0 8.09 5.363 13.27 13.676 13.27 3.64.003 7.23-.983 10.235-2.908zm49.338-10.36c0-7.893-5.51-13.27-12.938-13.27-7.43 0-12.938 5.377-12.938 13.27 0 7.896 5.51 13.272 12.937 13.272 7.43 0 12.94-5.376 12.94-13.27zM155.91 59.927V23.17h-6.542v36.755h6.542zm13.233-33.108c0-2.368-1.77-4.144-4.082-4.144-2.36 0-4.13 1.776-4.13 4.144 0 2.368 1.77 4.144 4.132 4.144 2.31 0 4.08-1.776 4.08-4.144zM48.916 35.276l-6.39-29.74-20.03 4.215 6.385 29.74L0 45.563 5.1 69.32 55.89 80l11.614-25.813L73.03 2.12 52.66 0l-3.744 35.276zM32.712 51.552c-1.466-.482-2.118-2.237-1.562-4.36.414-1.575 2.362-1.81 3.337-1.828.37-.006.735.068 1.07.218.69.31 1.857.968 2.094 1.973.343 1.45.013 2.667-1.034 3.616-1.05.953-2.434.866-3.904.382zm13.882 1.788c-1.324-.642-1.314-2.277-1.17-3.274.078-.543.298-1.055.642-1.487.473-.594 1.263-1.367 2.175-1.39 1.485-.04 2.762.62 3.483 1.812.724 1.188.362 2.498-.4 3.81-.767 1.307-2.764 1.48-4.73.528z"
        fill="#00CCBC"
        fillRule="evenodd"
      />
    </svg>
  );
}
